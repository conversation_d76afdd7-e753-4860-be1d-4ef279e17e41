<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剪贴板测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .log {
            margin-top: 20px;
            padding: 15px;
            background-color: #f1f3f4;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warn { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>剪贴板功能测试</h1>
        
        <div>
            <button onclick="testGetClipboard()">获取剪贴板内容</button>
            <button onclick="testNavigatorClipboard()">使用navigator.clipboard</button>
            <button onclick="testRefreshClipboard()">强制刷新剪贴板</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>剪贴板内容：</h3>
            <p id="clipboardContent"></p>
        </div>
        
        <div class="log">
            <h3>日志：</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let resultDiv = document.getElementById('result');
        let clipboardContentP = document.getElementById('clipboardContent');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function showResult(content) {
            clipboardContentP.textContent = content || '(空)';
            resultDiv.style.display = 'block';
        }

        function clearLog() {
            logContainer.innerHTML = '';
            resultDiv.style.display = 'none';
        }

        // 测试直接获取剪贴板
        function testGetClipboard() {
            log('开始测试直接获取剪贴板...', 'info');
            try {
                if (window.getClipboardText) {
                    const content = window.getClipboardText();
                    log(`获取到剪贴板内容: "${content}"`, 'success');
                    showResult(content);
                } else {
                    log('window.getClipboardText 方法不存在', 'error');
                }
            } catch (error) {
                log(`获取剪贴板失败: ${error.message}`, 'error');
            }
        }

        // 测试navigator.clipboard
        async function testNavigatorClipboard() {
            log('开始测试navigator.clipboard...', 'info');
            try {
                if (navigator.clipboard && navigator.clipboard.readText) {
                    const content = await navigator.clipboard.readText();
                    log(`通过navigator.clipboard获取到内容: "${content}"`, 'success');
                    showResult(content);
                } else {
                    log('navigator.clipboard.readText 不可用', 'error');
                }
            } catch (error) {
                log(`navigator.clipboard获取失败: ${error.message}`, 'error');
            }
        }

        // 测试强制刷新剪贴板
        function testRefreshClipboard() {
            log('开始测试强制刷新剪贴板...', 'info');
            try {
                if (window.refreshClipboard) {
                    const content = window.refreshClipboard();
                    log(`强制刷新后获取到内容: "${content}"`, 'success');
                    showResult(content);
                } else {
                    log('window.refreshClipboard 方法不存在', 'error');
                }
            } catch (error) {
                log(`强制刷新剪贴板失败: ${error.message}`, 'error');
            }
        }

        // 设置剪贴板更改监听
        if (window.setClipboardChangeCallback) {
            window.setClipboardChangeCallback(function() {
                log('检测到剪贴板内容更改！', 'warn');
                // 自动获取新内容
                setTimeout(() => {
                    testGetClipboard();
                }, 100);
            });
            log('剪贴板更改监听已设置', 'info');
        }

        // 监听剪贴板更改事件
        window.addEventListener('clipboardChanged', function() {
            log('收到clipboardChanged事件', 'warn');
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...', 'info');
            
            // 检查Android接口
            if (window.Android) {
                log('Android接口可用', 'success');
                if (window.Android.getClipboardText) {
                    log('getClipboardText方法可用', 'success');
                }
                if (window.Android.refreshClipboard) {
                    log('refreshClipboard方法可用', 'success');
                }
            } else {
                log('Android接口不可用', 'error');
            }
            
            // 检查全局方法
            if (window.getClipboardText) {
                log('window.getClipboardText可用', 'success');
            }
            if (window.refreshClipboard) {
                log('window.refreshClipboard可用', 'success');
            }
            
            log('初始化完成', 'info');
        });
    </script>
</body>
</html>
