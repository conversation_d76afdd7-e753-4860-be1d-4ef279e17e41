# Logo图片替换指南

## 📁 需要替换的文件

请将以下PNG图片文件放置到对应的目录中，替换现有的XML占位符：

### seeq_logo.png (注意：全小写)
- **路径**: `app/src/main/res/drawable/seeq_logo.png`
- **用途**: 启动动画的logo图片（整个动画过程使用同一张图片）
- **尺寸**: 自适应图片原始分辨率，最大200x200dp
- **格式**: PNG（支持透明背景）
- **文件名**: 必须是 `seeq_logo.png`（全小写，下划线分隔）
- **分辨率**: 支持任意分辨率，应用会自动适配

**注意**:
- 现在只需要一张logo图片，不再需要seeq_app.png
- 应用会自动检测图片的实际尺寸并适配动画

## ⚠️ 重要：Android文件命名规范
- Android资源文件名只能包含：**小写字母a-z**、**数字0-9**、**下划线_**
- 不能使用：大写字母、连字符(-)、空格或其他特殊字符
- 正确：`seeq_app.png`
- 错误：`Seeq-App.png`、`SeeqApp.png`、`seeq-app.png`

## 🎬 动画效果说明

### 启动动画序列：
1. **入场动画**：Seeq-App.png 淡入 + 缩放 + 向上移动
2. **加载指示器**：延迟400ms后淡入
3. **页面加载**：显示动态加载文本

### 退出动画序列：
1. **向上移动**：Logo以ease-out动画曲线（DecelerateInterpolator）向上移动到屏幕顶部
2. **同步缩放**：在向上移动过程中，Logo同时缩放到原来的50%
3. **提前淡出**：在到达顶部前25%的位置开始淡出（移动动画75%时开始）
4. **圆角效果**：Logo图片具有24dp的大圆角，提供现代化视觉效果
5. **元素隐藏**：其他UI元素（加载指示器、副标题等）淡出
6. **自适应尺寸**：动画自动适配logo图片的实际分辨率

## 🔧 技术实现

- 使用属性动画（ViewPropertyAnimator）确保流畅性能
- ease-out动画曲线（DecelerateInterpolator）创造自然的减速效果
- 24dp圆角通过clipToOutline和shape drawable实现
- 同步缩放和移动动画提供丰富的视觉层次
- 自适应图片尺寸，支持任意分辨率的logo
- 精确的时序控制：75%移动完成时开始淡出
- 分阶段动画确保视觉层次清晰

## 📱 用户体验

- **流畅过渡**：从启动logo到最终logo的自然变化
- **品牌一致性**：保持Seeq品牌视觉连贯性
- **性能优化**：使用硬件加速的属性动画
- **视觉反馈**：清晰的加载状态指示

## 📋 替换步骤

1. **重命名您的图片文件**：
   - 将 `Seeq-logo.png` 重命名为 `seeq_logo.png`

2. **放置文件**：
   - 将重命名后的PNG文件放到 `app/src/main/res/drawable/` 目录

3. **清理项目**：
   - 在Android Studio中执行 Build → Clean Project
   - 然后执行 Build → Rebuild Project

## ⚠️ 注意事项

1. **文件命名**：严格按照 `seeq_logo.png` 命名（全小写）
2. **透明背景**：建议使用透明背景PNG，适配不同主题
3. **分辨率**：提供高分辨率图片以支持高DPI设备
4. **命名规范**：Android不允许资源文件名包含大写字母或连字符
5. **圆角效果**：图片会自动应用24dp圆角效果

替换完成后，重新编译应用即可看到新的启动动画效果！🚀
