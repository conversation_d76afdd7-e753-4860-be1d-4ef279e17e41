<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 使用Compose主题的颜色值，但保留原有名称 -->
    <color name="purple_200">#FFFFBCE6</color>  <!-- Purple80 -->
    <color name="purple_500">#FFFF67A4</color>  <!-- Purple40 -->
    <color name="purple_700">#FF715B68</color>  <!-- PurpleGrey40 -->
    <color name="teal_200">#FFEFB8C8</color>    <!-- Pink80 -->
    <color name="teal_700">#FF7D5260</color>    <!-- Pink40 -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 底部模态框颜色 -->
    <color name="bottom_sheet_background">#FFFFFF</color>
    <color name="bottom_sheet_handle">#E0E0E0</color>
    <color name="bottom_sheet_divider">#F0F0F0</color>

    <!-- 文本颜色 -->
    <color name="text_primary">#1C1B1F</color>
    <color name="text_secondary">#49454F</color>
    <color name="text_hint">#79747E</color>

    <!-- 主题色 - 使用Compose亮色主题配色 -->
    <color name="primary">#FFFF67A4</color>           <!-- Purple40 -->
    <color name="primary_container">#FFFFBCE6</color> <!-- Purple80 -->
    <color name="on_primary">#FFFFFF</color>
    <color name="on_primary_container">#FF715B68</color> <!-- PurpleGrey40 -->

    <!-- 表面色 -->
    <color name="surface">#FFFBFE</color>
    <color name="surface_variant">#E7E0EC</color>
    <color name="on_surface">#1C1B1F</color>
    <color name="on_surface_variant">#49454F</color>

    <!-- 错误色 -->
    <color name="error">#BA1A1A</color>
    <color name="error_container">#FFDAD6</color>
    <color name="on_error">#FFFFFF</color>
    <color name="on_error_container">#410002</color>
</resources>