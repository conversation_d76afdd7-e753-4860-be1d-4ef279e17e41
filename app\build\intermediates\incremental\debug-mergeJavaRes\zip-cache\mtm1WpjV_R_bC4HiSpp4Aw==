[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "androidx/constraintlayout/solver/ArrayRow.class", "name": "androidx/constraintlayout/solver/ArrayRow.class", "size": 15724, "crc": 131677127}, {"key": "androidx/constraintlayout/solver/PriorityGoalRow.class", "name": "androidx/constraintlayout/solver/PriorityGoalRow.class", "size": 5107, "crc": 521603750}, {"key": "androidx/constraintlayout/solver/SolverVariable$Type.class", "name": "androidx/constraintlayout/solver/SolverVariable$Type.class", "size": 1393, "crc": -861547397}, {"key": "androidx/constraintlayout/solver/SolverVariableValues.class", "name": "androidx/constraintlayout/solver/SolverVariableValues.class", "size": 9456, "crc": 1467280676}, {"key": "androidx/constraintlayout/solver/LinearSystem.class", "name": "androidx/constraintlayout/solver/LinearSystem.class", "size": 24542, "crc": -1824554963}, {"key": "androidx/constraintlayout/solver/GoalRow.class", "name": "androidx/constraintlayout/solver/GoalRow.class", "size": 754, "crc": 385043280}, {"key": "androidx/constraintlayout/solver/LinearSystem$ValuesRow.class", "name": "androidx/constraintlayout/solver/LinearSystem$ValuesRow.class", "size": 1070, "crc": -1524490998}, {"key": "androidx/constraintlayout/solver/Pools.class", "name": "androidx/constraintlayout/solver/Pools.class", "size": 522, "crc": 1135091770}, {"key": "androidx/constraintlayout/solver/state/Reference.class", "name": "androidx/constraintlayout/solver/state/Reference.class", "size": 430, "crc": 481829019}, {"key": "androidx/constraintlayout/solver/state/State$1.class", "name": "androidx/constraintlayout/solver/state/State$1.class", "size": 1017, "crc": -1896701706}, {"key": "androidx/constraintlayout/solver/state/ConstraintReference$ConstraintReferenceFactory.class", "name": "androidx/constraintlayout/solver/state/ConstraintReference$ConstraintReferenceFactory.class", "size": 437, "crc": -1231726907}, {"key": "androidx/constraintlayout/solver/state/State$Direction.class", "name": "androidx/constraintlayout/solver/state/State$Direction.class", "size": 1435, "crc": -645120624}, {"key": "androidx/constraintlayout/solver/state/State$Helper.class", "name": "androidx/constraintlayout/solver/state/State$Helper.class", "size": 1517, "crc": -1921970687}, {"key": "androidx/constraintlayout/solver/state/State.class", "name": "androidx/constraintlayout/solver/state/State.class", "size": 10660, "crc": -1556466999}, {"key": "androidx/constraintlayout/solver/state/HelperReference.class", "name": "androidx/constraintlayout/solver/state/HelperReference.class", "size": 1921, "crc": -609823723}, {"key": "androidx/constraintlayout/solver/state/Dimension.class", "name": "androidx/constraintlayout/solver/state/Dimension.class", "size": 5419, "crc": -1424746946}, {"key": "androidx/constraintlayout/solver/state/State$Constraint.class", "name": "androidx/constraintlayout/solver/state/State$Constraint.class", "size": 2065, "crc": 1893087309}, {"key": "androidx/constraintlayout/solver/state/ConstraintReference.class", "name": "androidx/constraintlayout/solver/state/ConstraintReference.class", "size": 14711, "crc": -1517152315}, {"key": "androidx/constraintlayout/solver/state/State$Chain.class", "name": "androidx/constraintlayout/solver/state/State$Chain.class", "size": 1264, "crc": 1114383453}, {"key": "androidx/constraintlayout/solver/state/ConstraintReference$IncorrectConstraintException.class", "name": "androidx/constraintlayout/solver/state/ConstraintReference$IncorrectConstraintException.class", "size": 1431, "crc": -487732515}, {"key": "androidx/constraintlayout/solver/state/Dimension$Type.class", "name": "androidx/constraintlayout/solver/state/Dimension$Type.class", "size": 1348, "crc": 1440624230}, {"key": "androidx/constraintlayout/solver/state/helpers/AlignHorizontallyReference.class", "name": "androidx/constraintlayout/solver/state/helpers/AlignHorizontallyReference.class", "size": 2682, "crc": 688403115}, {"key": "androidx/constraintlayout/solver/state/helpers/HorizontalChainReference.class", "name": "androidx/constraintlayout/solver/state/helpers/HorizontalChainReference.class", "size": 3336, "crc": 1540380954}, {"key": "androidx/constraintlayout/solver/state/helpers/BarrierReference$1.class", "name": "androidx/constraintlayout/solver/state/helpers/BarrierReference$1.class", "size": 1128, "crc": 1189004042}, {"key": "androidx/constraintlayout/solver/state/helpers/ChainReference.class", "name": "androidx/constraintlayout/solver/state/helpers/ChainReference.class", "size": 1453, "crc": 403377144}, {"key": "androidx/constraintlayout/solver/state/helpers/GuidelineReference.class", "name": "androidx/constraintlayout/solver/state/helpers/GuidelineReference.class", "size": 2496, "crc": -1553924198}, {"key": "androidx/constraintlayout/solver/state/helpers/AlignVerticallyReference.class", "name": "androidx/constraintlayout/solver/state/helpers/AlignVerticallyReference.class", "size": 2676, "crc": 2067501440}, {"key": "androidx/constraintlayout/solver/state/helpers/BarrierReference.class", "name": "androidx/constraintlayout/solver/state/helpers/BarrierReference.class", "size": 2383, "crc": -1537464678}, {"key": "androidx/constraintlayout/solver/state/helpers/HorizontalChainReference$1.class", "name": "androidx/constraintlayout/solver/state/helpers/HorizontalChainReference$1.class", "size": 1007, "crc": 225757420}, {"key": "androidx/constraintlayout/solver/state/helpers/VerticalChainReference.class", "name": "androidx/constraintlayout/solver/state/helpers/VerticalChainReference.class", "size": 3328, "crc": -1458462840}, {"key": "androidx/constraintlayout/solver/state/helpers/VerticalChainReference$1.class", "name": "androidx/constraintlayout/solver/state/helpers/VerticalChainReference$1.class", "size": 1001, "crc": 1110333595}, {"key": "androidx/constraintlayout/solver/state/ConstraintReference$1.class", "name": "androidx/constraintlayout/solver/state/ConstraintReference$1.class", "size": 1686, "crc": -1820036258}, {"key": "androidx/constraintlayout/solver/LinearSystem$Row.class", "name": "androidx/constraintlayout/solver/LinearSystem$Row.class", "size": 922, "crc": 1709635986}, {"key": "androidx/constraintlayout/solver/ArrayLinkedVariables.class", "name": "androidx/constraintlayout/solver/ArrayLinkedVariables.class", "size": 9297, "crc": -1282838703}, {"key": "androidx/constraintlayout/solver/Pools$Pool.class", "name": "androidx/constraintlayout/solver/Pools$Pool.class", "size": 456, "crc": 1187697929}, {"key": "androidx/constraintlayout/solver/SolverVariable$1.class", "name": "androidx/constraintlayout/solver/SolverVariable$1.class", "size": 1000, "crc": 1588270456}, {"key": "androidx/constraintlayout/solver/PriorityGoalRow$GoalVariableAccessor.class", "name": "androidx/constraintlayout/solver/PriorityGoalRow$GoalVariableAccessor.class", "size": 3503, "crc": 2030153716}, {"key": "androidx/constraintlayout/solver/Cache.class", "name": "androidx/constraintlayout/solver/Cache.class", "size": 1131, "crc": -1515659375}, {"key": "androidx/constraintlayout/solver/Metrics.class", "name": "androidx/constraintlayout/solver/Metrics.class", "size": 2828, "crc": 737643001}, {"key": "androidx/constraintlayout/solver/ArrayRow$ArrayRowVariables.class", "name": "androidx/constraintlayout/solver/ArrayRow$ArrayRowVariables.class", "size": 993, "crc": -1161397053}, {"key": "androidx/constraintlayout/solver/PriorityGoalRow$1.class", "name": "androidx/constraintlayout/solver/PriorityGoalRow$1.class", "size": 1287, "crc": -1551115149}, {"key": "androidx/constraintlayout/solver/Pools$SimplePool.class", "name": "androidx/constraintlayout/solver/Pools$SimplePool.class", "size": 2003, "crc": -401652640}, {"key": "androidx/constraintlayout/solver/widgets/Barrier.class", "name": "androidx/constraintlayout/solver/widgets/Barrier.class", "size": 6332, "crc": 1573206616}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintWidget.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintWidget.class", "size": 46865, "crc": 1236973659}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintWidgetContainer.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintWidgetContainer.class", "size": 14602, "crc": -1519825494}, {"key": "androidx/constraintlayout/solver/widgets/Flow.class", "name": "androidx/constraintlayout/solver/widgets/Flow.class", "size": 18641, "crc": 40498261}, {"key": "androidx/constraintlayout/solver/widgets/WidgetContainer.class", "name": "androidx/constraintlayout/solver/widgets/WidgetContainer.class", "size": 3480, "crc": -804173560}, {"key": "androidx/constraintlayout/solver/widgets/Guideline.class", "name": "androidx/constraintlayout/solver/widgets/Guideline.class", "size": 8283, "crc": -1122067847}, {"key": "androidx/constraintlayout/solver/widgets/HelperWidget.class", "name": "androidx/constraintlayout/solver/widgets/HelperWidget.class", "size": 2271, "crc": 1246138458}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintAnchor$1.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintAnchor$1.class", "size": 1245, "crc": 707770356}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintWidget$1.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintWidget$1.class", "size": 1929, "crc": -1835111045}, {"key": "androidx/constraintlayout/solver/widgets/Optimizer.class", "name": "androidx/constraintlayout/solver/widgets/Optimizer.class", "size": 3475, "crc": 1334397590}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintWidget$DimensionBehaviour.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintWidget$DimensionBehaviour.class", "size": 1512, "crc": -563740865}, {"key": "androidx/constraintlayout/solver/widgets/Chain.class", "name": "androidx/constraintlayout/solver/widgets/Chain.class", "size": 10697, "crc": -198725273}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintAnchor$Type.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintAnchor$Type.class", "size": 1639, "crc": 384456013}, {"key": "androidx/constraintlayout/solver/widgets/Helper.class", "name": "androidx/constraintlayout/solver/widgets/Helper.class", "size": 344, "crc": 2087959585}, {"key": "androidx/constraintlayout/solver/widgets/Guideline$1.class", "name": "androidx/constraintlayout/solver/widgets/Guideline$1.class", "size": 1287, "crc": 1894480920}, {"key": "androidx/constraintlayout/solver/widgets/Flow$WidgetsList.class", "name": "androidx/constraintlayout/solver/widgets/Flow$WidgetsList.class", "size": 10210, "crc": -1328218406}, {"key": "androidx/constraintlayout/solver/widgets/ConstraintAnchor.class", "name": "androidx/constraintlayout/solver/widgets/ConstraintAnchor.class", "size": 9194, "crc": 1034200250}, {"key": "androidx/constraintlayout/solver/widgets/VirtualLayout.class", "name": "androidx/constraintlayout/solver/widgets/VirtualLayout.class", "size": 6478, "crc": 1175357429}, {"key": "androidx/constraintlayout/solver/widgets/Rectangle.class", "name": "androidx/constraintlayout/solver/widgets/Rectangle.class", "size": 1306, "crc": 621228662}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/HorizontalWidgetRun$1.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/HorizontalWidgetRun$1.class", "size": 1072, "crc": -1480828273}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/HelperReferences.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/HelperReferences.class", "size": 4660, "crc": -1201930916}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$MeasureType.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$MeasureType.class", "size": 1268, "crc": -892190987}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/DependencyNode.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/DependencyNode.class", "size": 4238, "crc": 1906390786}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/DimensionDependency.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/DimensionDependency.class", "size": 1753, "crc": 138017876}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/DependencyGraph.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/DependencyGraph.class", "size": 25411, "crc": 170428474}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/VerticalWidgetRun.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/VerticalWidgetRun.class", "size": 10832, "crc": 379269764}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$Measure.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$Measure.class", "size": 1034, "crc": 1293102258}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/GuidelineReference.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/GuidelineReference.class", "size": 3718, "crc": 242126234}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure.class", "size": 11298, "crc": 501282704}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/ChainRun.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/ChainRun.class", "size": 12063, "crc": -1952457796}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/HorizontalWidgetRun.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/HorizontalWidgetRun.class", "size": 13527, "crc": 1249673241}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/Dependency.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/Dependency.class", "size": 236, "crc": 2058502133}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/DependencyNode$Type.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/DependencyNode$Type.class", "size": 1660, "crc": 190496505}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/RunGroup.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/RunGroup.class", "size": 6525, "crc": 1024222648}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/VerticalWidgetRun$1.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/VerticalWidgetRun$1.class", "size": 1066, "crc": 86285464}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun$RunType.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun$RunType.class", "size": 1411, "crc": 614037427}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun$1.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun$1.class", "size": 1111, "crc": 331506663}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/WidgetRun.class", "size": 8780, "crc": -1691404591}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$Measurer.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/BasicMeasure$Measurer.class", "size": 551, "crc": 917372776}, {"key": "androidx/constraintlayout/solver/widgets/analyzer/BaselineDimensionDependency.class", "name": "androidx/constraintlayout/solver/widgets/analyzer/BaselineDimensionDependency.class", "size": 1429, "crc": 1469078143}, {"key": "androidx/constraintlayout/solver/widgets/ChainHead.class", "name": "androidx/constraintlayout/solver/widgets/ChainHead.class", "size": 5343, "crc": -265798757}, {"key": "androidx/constraintlayout/solver/SolverVariable.class", "name": "androidx/constraintlayout/solver/SolverVariable.class", "size": 6390, "crc": 1842890025}]