[{"key": "com/google/android/material/animation/AnimatableView$Listener.class", "name": "com/google/android/material/animation/AnimatableView$Listener.class", "size": 287, "crc": -1363297199}, {"key": "com/google/android/material/animation/AnimatableView.class", "name": "com/google/android/material/animation/AnimatableView.class", "size": 464, "crc": -1020481189}, {"key": "com/google/android/material/animation/AnimationUtils.class", "name": "com/google/android/material/animation/AnimationUtils.class", "size": 1858, "crc": 1452017943}, {"key": "com/google/android/material/animation/AnimatorSetCompat.class", "name": "com/google/android/material/animation/AnimatorSetCompat.class", "size": 1893, "crc": -954195287}, {"key": "com/google/android/material/animation/ArgbEvaluatorCompat.class", "name": "com/google/android/material/animation/ArgbEvaluatorCompat.class", "size": 2026, "crc": -1280127495}, {"key": "com/google/android/material/animation/ChildrenAlphaProperty.class", "name": "com/google/android/material/animation/ChildrenAlphaProperty.class", "size": 2224, "crc": -997740139}, {"key": "com/google/android/material/animation/DrawableAlphaProperty.class", "name": "com/google/android/material/animation/DrawableAlphaProperty.class", "size": 2329, "crc": 1905436735}, {"key": "com/google/android/material/animation/ImageMatrixProperty.class", "name": "com/google/android/material/animation/ImageMatrixProperty.class", "size": 1569, "crc": 1102498974}, {"key": "com/google/android/material/animation/MatrixEvaluator.class", "name": "com/google/android/material/animation/MatrixEvaluator.class", "size": 1447, "crc": -679066624}, {"key": "com/google/android/material/animation/MotionSpec.class", "name": "com/google/android/material/animation/MotionSpec.class", "size": 7905, "crc": -955884567}, {"key": "com/google/android/material/animation/MotionTiming.class", "name": "com/google/android/material/animation/MotionTiming.class", "size": 4608, "crc": -2070969771}, {"key": "com/google/android/material/animation/Positioning.class", "name": "com/google/android/material/animation/Positioning.class", "size": 504, "crc": 87312119}, {"key": "com/google/android/material/animation/TransformationCallback.class", "name": "com/google/android/material/animation/TransformationCallback.class", "size": 333, "crc": 486063317}, {"key": "com/google/android/material/appbar/AppBarLayout$1.class", "name": "com/google/android/material/appbar/AppBarLayout$1.class", "size": 1116, "crc": -830466912}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$1.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$1.class", "size": 1878, "crc": 148096986}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$2.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$2.class", "size": 1745, "crc": -1761908520}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$3.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$3.class", "size": 2203, "crc": -1767465440}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$4.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$4.class", "size": 2048, "crc": -1988874232}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$BaseDragCallback.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$BaseDragCallback.class", "size": 981, "crc": -1279295162}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState$1.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState$1.class", "size": 2250, "crc": -1221226705}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState.class", "size": 2367, "crc": -1844129691}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior.class", "size": 27924, "crc": 720871288}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseOnOffsetChangedListener.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseOnOffsetChangedListener.class", "size": 476, "crc": 926522853}, {"key": "com/google/android/material/appbar/AppBarLayout$Behavior$DragCallback.class", "name": "com/google/android/material/appbar/AppBarLayout$Behavior$DragCallback.class", "size": 885, "crc": -439291752}, {"key": "com/google/android/material/appbar/AppBarLayout$Behavior.class", "name": "com/google/android/material/appbar/AppBarLayout$Behavior.class", "size": 4528, "crc": -993608016}, {"key": "com/google/android/material/appbar/AppBarLayout$ChildScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$ChildScrollEffect.class", "size": 664, "crc": 1896911646}, {"key": "com/google/android/material/appbar/AppBarLayout$CompressChildScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$CompressChildScrollEffect.class", "size": 2211, "crc": 1432441098}, {"key": "com/google/android/material/appbar/AppBarLayout$DrawableHelperV29.class", "name": "com/google/android/material/appbar/AppBarLayout$DrawableHelperV29.class", "size": 1177, "crc": -1628563656}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollEffect.class", "size": 771, "crc": -556025883}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollFlags.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollFlags.class", "size": 769, "crc": 942742173}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams.class", "size": 5529, "crc": -1974385990}, {"key": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollListener.class", "name": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollListener.class", "size": 420, "crc": -767283174}, {"key": "com/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener.class", "name": "com/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener.class", "size": 646, "crc": 1374919369}, {"key": "com/google/android/material/appbar/AppBarLayout$ScrollingViewBehavior.class", "name": "com/google/android/material/appbar/AppBarLayout$ScrollingViewBehavior.class", "size": 8226, "crc": -2049855475}, {"key": "com/google/android/material/appbar/AppBarLayout.class", "name": "com/google/android/material/appbar/AppBarLayout.class", "size": 30103, "crc": -1607620356}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$1.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$1.class", "size": 1268, "crc": 2115446291}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$2.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$2.class", "size": 1227, "crc": -1676979969}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$LayoutParams.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$LayoutParams.class", "size": 3528, "crc": 737246342}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$OffsetUpdateListener.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$OffsetUpdateListener.class", "size": 3411, "crc": -1241536411}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$StaticLayoutBuilderConfigurer.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$StaticLayoutBuilderConfigurer.class", "size": 689, "crc": -774284876}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$TitleCollapseMode.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$TitleCollapseMode.class", "size": 712, "crc": 2117541193}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout.class", "size": 35927, "crc": 94769121}, {"key": "com/google/android/material/appbar/HeaderBehavior$FlingRunnable.class", "name": "com/google/android/material/appbar/HeaderBehavior$FlingRunnable.class", "size": 1796, "crc": -1425414233}, {"key": "com/google/android/material/appbar/HeaderBehavior.class", "name": "com/google/android/material/appbar/HeaderBehavior.class", "size": 7317, "crc": 1783067515}, {"key": "com/google/android/material/appbar/HeaderScrollingViewBehavior.class", "name": "com/google/android/material/appbar/HeaderScrollingViewBehavior.class", "size": 5847, "crc": -1321163839}, {"key": "com/google/android/material/appbar/MaterialToolbar.class", "name": "com/google/android/material/appbar/MaterialToolbar.class", "size": 11017, "crc": -101096}, {"key": "com/google/android/material/appbar/ViewOffsetBehavior.class", "name": "com/google/android/material/appbar/ViewOffsetBehavior.class", "size": 3616, "crc": -1834279365}, {"key": "com/google/android/material/appbar/ViewOffsetHelper.class", "name": "com/google/android/material/appbar/ViewOffsetHelper.class", "size": 2141, "crc": 1268299768}, {"key": "com/google/android/material/appbar/ViewUtilsLollipop.class", "name": "com/google/android/material/appbar/ViewUtilsLollipop.class", "size": 3297, "crc": -1952491534}, {"key": "com/google/android/material/badge/BadgeDrawable$1.class", "name": "com/google/android/material/badge/BadgeDrawable$1.class", "size": 1034, "crc": -1208308683}, {"key": "com/google/android/material/badge/BadgeDrawable$BadgeGravity.class", "name": "com/google/android/material/badge/BadgeDrawable$BadgeGravity.class", "size": 432, "crc": 1323430199}, {"key": "com/google/android/material/badge/BadgeDrawable.class", "name": "com/google/android/material/badge/BadgeDrawable.class", "size": 29466, "crc": -1206860186}, {"key": "com/google/android/material/badge/BadgeState$State$1.class", "name": "com/google/android/material/badge/BadgeState$State$1.class", "size": 1498, "crc": -1907659263}, {"key": "com/google/android/material/badge/BadgeState$State.class", "name": "com/google/android/material/badge/BadgeState$State.class", "size": 10621, "crc": -1273812697}, {"key": "com/google/android/material/badge/BadgeState.class", "name": "com/google/android/material/badge/BadgeState.class", "size": 19849, "crc": -231395282}, {"key": "com/google/android/material/badge/BadgeUtils$1.class", "name": "com/google/android/material/badge/BadgeUtils$1.class", "size": 1920, "crc": -1378954767}, {"key": "com/google/android/material/badge/BadgeUtils$2.class", "name": "com/google/android/material/badge/BadgeUtils$2.class", "size": 1565, "crc": -2133753313}, {"key": "com/google/android/material/badge/BadgeUtils$3.class", "name": "com/google/android/material/badge/BadgeUtils$3.class", "size": 1309, "crc": 1692333593}, {"key": "com/google/android/material/badge/BadgeUtils$4.class", "name": "com/google/android/material/badge/BadgeUtils$4.class", "size": 1203, "crc": -1331832818}, {"key": "com/google/android/material/badge/BadgeUtils.class", "name": "com/google/android/material/badge/BadgeUtils.class", "size": 8961, "crc": 509739047}, {"key": "com/google/android/material/badge/ExperimentalBadgeUtils.class", "name": "com/google/android/material/badge/ExperimentalBadgeUtils.class", "size": 806, "crc": 592216991}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$1.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$1.class", "size": 1226, "crc": -165105925}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$OnScrollStateChangedListener.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$OnScrollStateChangedListener.class", "size": 674, "crc": 216903870}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$ScrollState.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$ScrollState.class", "size": 593, "crc": 1329916066}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior.class", "size": 9456, "crc": -1922780440}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$1.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$1.class", "size": 5459, "crc": -601422144}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$2.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$2.class", "size": 2195, "crc": 1910280488}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$OnDismissListener.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$OnDismissListener.class", "size": 371, "crc": 1848196702}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$SettleRunnable.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$SettleRunnable.class", "size": 1663, "crc": -525327386}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior.class", "size": 7651, "crc": 725924203}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$1.class", "size": 1155, "crc": -14450562}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$2.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$2.class", "size": 2824, "crc": -377197263}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$3.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$3.class", "size": 2443, "crc": 976692738}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$4.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$4.class", "size": 1158, "crc": -1886384398}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$5$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$5$1.class", "size": 1323, "crc": -930294984}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$5.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$5.class", "size": 1663, "crc": -1114086171}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$6.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$6.class", "size": 1255, "crc": 1454291677}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$7.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$7.class", "size": 1598, "crc": -1237902107}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$8.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$8.class", "size": 1238, "crc": 169665284}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$9.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$9.class", "size": 1532, "crc": -1992389726}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$AnimationListener.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$AnimationListener.class", "size": 387, "crc": 509838026}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$Behavior$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$Behavior$1.class", "size": 4276, "crc": 376320484}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$Behavior.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$Behavior.class", "size": 5759, "crc": -1694109903}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAlignmentMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAlignmentMode.class", "size": 451, "crc": -416526033}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAnchorMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAnchorMode.class", "size": 683, "crc": -1698493628}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAnimationMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAnimationMode.class", "size": 451, "crc": -1536316442}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$MenuAlignmentMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$MenuAlignmentMode.class", "size": 691, "crc": 631780464}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$SavedState$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$SavedState$1.class", "size": 2114, "crc": 778132860}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$SavedState.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$SavedState.class", "size": 1786, "crc": -1706190112}, {"key": "com/google/android/material/bottomappbar/BottomAppBar.class", "name": "com/google/android/material/bottomappbar/BottomAppBar.class", "size": 34852, "crc": 1261559453}, {"key": "com/google/android/material/bottomappbar/BottomAppBarTopEdgeTreatment.class", "name": "com/google/android/material/bottomappbar/BottomAppBarTopEdgeTreatment.class", "size": 4441, "crc": 1211841474}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationItemView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationItemView.class", "size": 1369, "crc": 1233276601}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationMenuView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationMenuView.class", "size": 6106, "crc": -2072864072}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$1.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$1.class", "size": 2281, "crc": -1729924553}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemReselectedListener.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemReselectedListener.class", "size": 610, "crc": -12269829}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemSelectedListener.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemSelectedListener.class", "size": 602, "crc": -950744650}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView.class", "size": 8211, "crc": -1343449937}, {"key": "com/google/android/material/bottomnavigation/LabelVisibilityMode.class", "name": "com/google/android/material/bottomnavigation/LabelVisibilityMode.class", "size": 674, "crc": 437046173}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$1.class", "size": 1088, "crc": 768781991}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$2.class", "size": 1228, "crc": 911723455}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$3.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$3.class", "size": 1585, "crc": 446721298}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$4.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$4.class", "size": 3863, "crc": 489074595}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$5.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$5.class", "size": 5199, "crc": 556890234}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$6.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$6.class", "size": 1522, "crc": 481306422}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$BottomSheetCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$BottomSheetCallback.class", "size": 868, "crc": -1399764635}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SaveFlags.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SaveFlags.class", "size": 694, "crc": -2123549584}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState$1.class", "size": 2169, "crc": 1168598019}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState.class", "size": 3130, "crc": -1847846970}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StableState.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StableState.class", "size": 698, "crc": 421477145}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$State.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$State.class", "size": 686, "crc": 265145781}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker$1.class", "size": 1705, "crc": 1873126233}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker.class", "size": 2405, "crc": 1852340488}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior.class", "size": 49830, "crc": -458524431}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$1.class", "size": 2678, "crc": 727071334}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$2.class", "size": 1258, "crc": -1039876849}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$3.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$3.class", "size": 1803, "crc": -1284202404}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$4.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$4.class", "size": 1144, "crc": 1046567602}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$5.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$5.class", "size": 1276, "crc": 1092683827}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$EdgeToEdgeCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$EdgeToEdgeCallback.class", "size": 4514, "crc": -2011229946}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog.class", "size": 12024, "crc": 1510368451}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$1.class", "size": 303, "crc": -1734559629}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$BottomSheetDismissCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$BottomSheetDismissCallback.class", "size": 1766, "crc": -1230958855}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment.class", "size": 3705, "crc": 2058819514}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$1.class", "size": 1369, "crc": 1377018783}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$2.class", "size": 1393, "crc": 872862831}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView.class", "size": 10113, "crc": 82025669}, {"key": "com/google/android/material/bottomsheet/InsetsAnimationCallback.class", "name": "com/google/android/material/bottomsheet/InsetsAnimationCallback.class", "size": 3015, "crc": -1103437524}, {"key": "com/google/android/material/button/MaterialButton$IconGravity.class", "name": "com/google/android/material/button/MaterialButton$IconGravity.class", "size": 435, "crc": -81790229}, {"key": "com/google/android/material/button/MaterialButton$InspectionCompanion.class", "name": "com/google/android/material/button/MaterialButton$InspectionCompanion.class", "size": 2344, "crc": 1016066278}, {"key": "com/google/android/material/button/MaterialButton$OnCheckedChangeListener.class", "name": "com/google/android/material/button/MaterialButton$OnCheckedChangeListener.class", "size": 365, "crc": -1552301104}, {"key": "com/google/android/material/button/MaterialButton$OnPressedChangeListener.class", "name": "com/google/android/material/button/MaterialButton$OnPressedChangeListener.class", "size": 365, "crc": 331868981}, {"key": "com/google/android/material/button/MaterialButton$SavedState$1.class", "name": "com/google/android/material/button/MaterialButton$SavedState$1.class", "size": 2051, "crc": 543509140}, {"key": "com/google/android/material/button/MaterialButton$SavedState.class", "name": "com/google/android/material/button/MaterialButton$SavedState.class", "size": 2021, "crc": -1296956776}, {"key": "com/google/android/material/button/MaterialButton.class", "name": "com/google/android/material/button/MaterialButton.class", "size": 26221, "crc": 1666616723}, {"key": "com/google/android/material/button/MaterialButtonHelper.class", "name": "com/google/android/material/button/MaterialButtonHelper.class", "size": 13452, "crc": 1491862909}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$1.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$1.class", "size": 1738, "crc": 1268162330}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$2.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$2.class", "size": 1887, "crc": -1350254361}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$CornerData.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$CornerData.class", "size": 2163, "crc": -2070010954}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$OnButtonCheckedListener.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$OnButtonCheckedListener.class", "size": 495, "crc": -1156291473}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$PressedStateTracker.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$PressedStateTracker.class", "size": 1594, "crc": -1995670850}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup.class", "size": 20661, "crc": 599458967}, {"key": "com/google/android/material/canvas/CanvasCompat$CanvasOperation.class", "name": "com/google/android/material/canvas/CanvasCompat$CanvasOperation.class", "size": 387, "crc": 1890714949}, {"key": "com/google/android/material/canvas/CanvasCompat.class", "name": "com/google/android/material/canvas/CanvasCompat.class", "size": 1666, "crc": -1551473624}, {"key": "com/google/android/material/card/MaterialCardView$CheckedIconGravity.class", "name": "com/google/android/material/card/MaterialCardView$CheckedIconGravity.class", "size": 451, "crc": 369309957}, {"key": "com/google/android/material/card/MaterialCardView$OnCheckedChangeListener.class", "name": "com/google/android/material/card/MaterialCardView$OnCheckedChangeListener.class", "size": 367, "crc": 1584533806}, {"key": "com/google/android/material/card/MaterialCardView.class", "name": "com/google/android/material/card/MaterialCardView.class", "size": 15419, "crc": 1122047300}, {"key": "com/google/android/material/card/MaterialCardViewHelper$1.class", "name": "com/google/android/material/card/MaterialCardViewHelper$1.class", "size": 1314, "crc": -656007334}, {"key": "com/google/android/material/card/MaterialCardViewHelper.class", "name": "com/google/android/material/card/MaterialCardViewHelper.class", "size": 21781, "crc": -740162465}, {"key": "com/google/android/material/carousel/Arrangement.class", "name": "com/google/android/material/carousel/Arrangement.class", "size": 3813, "crc": -1902871192}, {"key": "com/google/android/material/carousel/Carousel.class", "name": "com/google/android/material/carousel/Carousel.class", "size": 226, "crc": 267424411}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$1.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$1.class", "size": 1972, "crc": 588419512}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$ChildCalculations.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$ChildCalculations.class", "size": 1008, "crc": 2072727801}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$DebugItemDecoration.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$DebugItemDecoration.class", "size": 3433, "crc": 1737110059}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$KeylineRange.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$KeylineRange.class", "size": 1083, "crc": 1830183594}, {"key": "com/google/android/material/carousel/CarouselLayoutManager.class", "name": "com/google/android/material/carousel/CarouselLayoutManager.class", "size": 29068, "crc": -2081786500}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper$1.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper$1.class", "size": 3706, "crc": 322550078}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper$2.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper$2.class", "size": 3874, "crc": -942766608}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper.class", "size": 2559, "crc": 417921504}, {"key": "com/google/android/material/carousel/CarouselSnapHelper$1.class", "name": "com/google/android/material/carousel/CarouselSnapHelper$1.class", "size": 2813, "crc": -1677321292}, {"key": "com/google/android/material/carousel/CarouselSnapHelper.class", "name": "com/google/android/material/carousel/CarouselSnapHelper.class", "size": 6217, "crc": 501377151}, {"key": "com/google/android/material/carousel/CarouselStrategy.class", "name": "com/google/android/material/carousel/CarouselStrategy.class", "size": 963, "crc": 907623688}, {"key": "com/google/android/material/carousel/CarouselStrategyHelper.class", "name": "com/google/android/material/carousel/CarouselStrategyHelper.class", "size": 3551, "crc": 126850579}, {"key": "com/google/android/material/carousel/HeroCarouselStrategy.class", "name": "com/google/android/material/carousel/HeroCarouselStrategy.class", "size": 3380, "crc": -1931192320}, {"key": "com/google/android/material/carousel/KeylineState$1.class", "name": "com/google/android/material/carousel/KeylineState$1.class", "size": 258, "crc": 1327050306}, {"key": "com/google/android/material/carousel/KeylineState$Builder.class", "name": "com/google/android/material/carousel/KeylineState$Builder.class", "size": 4379, "crc": -1913408508}, {"key": "com/google/android/material/carousel/KeylineState$Keyline.class", "name": "com/google/android/material/carousel/KeylineState$Keyline.class", "size": 1227, "crc": 1836778538}, {"key": "com/google/android/material/carousel/KeylineState.class", "name": "com/google/android/material/carousel/KeylineState.class", "size": 4673, "crc": 1341212486}, {"key": "com/google/android/material/carousel/KeylineStateList.class", "name": "com/google/android/material/carousel/KeylineStateList.class", "size": 12505, "crc": -1501219641}, {"key": "com/google/android/material/carousel/Maskable.class", "name": "com/google/android/material/carousel/Maskable.class", "size": 795, "crc": 1122002191}, {"key": "com/google/android/material/carousel/MaskableFrameLayout.class", "name": "com/google/android/material/carousel/MaskableFrameLayout.class", "size": 7731, "crc": -1540262842}, {"key": "com/google/android/material/carousel/MultiBrowseCarouselStrategy.class", "name": "com/google/android/material/carousel/MultiBrowseCarouselStrategy.class", "size": 3955, "crc": 1022284538}, {"key": "com/google/android/material/carousel/OnMaskChangedListener.class", "name": "com/google/android/material/carousel/OnMaskChangedListener.class", "size": 298, "crc": -2023910105}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$1.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$1.class", "size": 1688, "crc": -1165054363}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$CheckedState.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$CheckedState.class", "size": 685, "crc": -152875898}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$OnCheckedStateChangedListener.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$OnCheckedStateChangedListener.class", "size": 490, "crc": 1337214408}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$OnErrorChangedListener.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$OnErrorChangedListener.class", "size": 461, "crc": -591545887}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$SavedState$1.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$SavedState$1.class", "size": 1554, "crc": -2014267477}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$SavedState.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$SavedState.class", "size": 2838, "crc": 471835473}, {"key": "com/google/android/material/checkbox/MaterialCheckBox.class", "name": "com/google/android/material/checkbox/MaterialCheckBox.class", "size": 22146, "crc": 901205424}, {"key": "com/google/android/material/chip/Chip$1.class", "name": "com/google/android/material/chip/Chip$1.class", "size": 1509, "crc": 1147829237}, {"key": "com/google/android/material/chip/Chip$2.class", "name": "com/google/android/material/chip/Chip$2.class", "size": 1301, "crc": -1971551756}, {"key": "com/google/android/material/chip/Chip$ChipTouchHelper.class", "name": "com/google/android/material/chip/Chip$ChipTouchHelper.class", "size": 5086, "crc": -1808527157}, {"key": "com/google/android/material/chip/Chip.class", "name": "com/google/android/material/chip/Chip.class", "size": 46021, "crc": 833168333}, {"key": "com/google/android/material/chip/ChipDrawable$Delegate.class", "name": "com/google/android/material/chip/ChipDrawable$Delegate.class", "size": 281, "crc": 354818190}, {"key": "com/google/android/material/chip/ChipDrawable.class", "name": "com/google/android/material/chip/ChipDrawable.class", "size": 48277, "crc": -1864952515}, {"key": "com/google/android/material/chip/ChipGroup$1.class", "name": "com/google/android/material/chip/ChipGroup$1.class", "size": 1723, "crc": 1619976515}, {"key": "com/google/android/material/chip/ChipGroup$2.class", "name": "com/google/android/material/chip/ChipGroup$2.class", "size": 1941, "crc": 1078764910}, {"key": "com/google/android/material/chip/ChipGroup$LayoutParams.class", "name": "com/google/android/material/chip/ChipGroup$LayoutParams.class", "size": 1208, "crc": 470342650}, {"key": "com/google/android/material/chip/ChipGroup$OnCheckedChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$OnCheckedChangeListener.class", "size": 543, "crc": 606309187}, {"key": "com/google/android/material/chip/ChipGroup$OnCheckedStateChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$OnCheckedStateChangeListener.class", "size": 561, "crc": 1637379387}, {"key": "com/google/android/material/chip/ChipGroup$PassThroughHierarchyChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$PassThroughHierarchyChangeListener.class", "size": 2420, "crc": -1673094190}, {"key": "com/google/android/material/chip/ChipGroup.class", "name": "com/google/android/material/chip/ChipGroup.class", "size": 12784, "crc": -1454759494}, {"key": "com/google/android/material/circularreveal/CircularRevealCompat$1.class", "name": "com/google/android/material/circularreveal/CircularRevealCompat$1.class", "size": 1378, "crc": 1396693513}, {"key": "com/google/android/material/circularreveal/CircularRevealCompat.class", "name": "com/google/android/material/circularreveal/CircularRevealCompat.class", "size": 3585, "crc": 717998457}, {"key": "com/google/android/material/circularreveal/CircularRevealFrameLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealFrameLayout.class", "size": 3387, "crc": 721982004}, {"key": "com/google/android/material/circularreveal/CircularRevealGridLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealGridLayout.class", "size": 3268, "crc": -296543340}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper$Delegate.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper$Delegate.class", "size": 367, "crc": 1824946489}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper$Strategy.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper$Strategy.class", "size": 463, "crc": -513609849}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper.class", "size": 8502, "crc": 124128771}, {"key": "com/google/android/material/circularreveal/CircularRevealLinearLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealLinearLayout.class", "size": 3276, "crc": 1993968684}, {"key": "com/google/android/material/circularreveal/CircularRevealRelativeLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealRelativeLayout.class", "size": 3284, "crc": -1300003018}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$1.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$1.class", "size": 294, "crc": -17679820}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealEvaluator.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealEvaluator.class", "size": 2287, "crc": 717294070}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealProperty.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealProperty.class", "size": 2413, "crc": 1723618276}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealScrimColorProperty.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealScrimColorProperty.class", "size": 2076, "crc": -1166667281}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$RevealInfo.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$RevealInfo.class", "size": 1720, "crc": -1145537665}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget.class", "size": 1760, "crc": 1680299546}, {"key": "com/google/android/material/circularreveal/cardview/CircularRevealCardView.class", "name": "com/google/android/material/circularreveal/cardview/CircularRevealCardView.class", "size": 3291, "crc": 840111139}, {"key": "com/google/android/material/circularreveal/coordinatorlayout/CircularRevealCoordinatorLayout.class", "name": "com/google/android/material/circularreveal/coordinatorlayout/CircularRevealCoordinatorLayout.class", "size": 3366, "crc": -303995348}, {"key": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks$1.class", "name": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks$1.class", "size": 1680, "crc": -1045796960}, {"key": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks.class", "name": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks.class", "size": 3958, "crc": -1749790148}, {"key": "com/google/android/material/color/ColorContrast.class", "name": "com/google/android/material/color/ColorContrast.class", "size": 3395, "crc": -1846034698}, {"key": "com/google/android/material/color/ColorContrastOptions$1.class", "name": "com/google/android/material/color/ColorContrastOptions$1.class", "size": 276, "crc": -550405037}, {"key": "com/google/android/material/color/ColorContrastOptions$Builder.class", "name": "com/google/android/material/color/ColorContrastOptions$Builder.class", "size": 1725, "crc": 983312651}, {"key": "com/google/android/material/color/ColorContrastOptions.class", "name": "com/google/android/material/color/ColorContrastOptions.class", "size": 1541, "crc": -1830527632}, {"key": "com/google/android/material/color/ColorResourcesLoaderCreator.class", "name": "com/google/android/material/color/ColorResourcesLoaderCreator.class", "size": 3655, "crc": 279357628}, {"key": "com/google/android/material/color/ColorResourcesOverride.class", "name": "com/google/android/material/color/ColorResourcesOverride.class", "size": 1449, "crc": 21569278}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$1.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$1.class", "size": 1346, "crc": -154954173}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ColorResource.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ColorResource.class", "size": 1612, "crc": -1796039007}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$PackageChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$PackageChunk.class", "size": 3814, "crc": 1748309831}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$PackageInfo.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$PackageInfo.class", "size": 978, "crc": -1576061381}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResChunkHeader.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResChunkHeader.class", "size": 1058, "crc": -1947385577}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResEntry.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResEntry.class", "size": 1312, "crc": -700134466}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResTable.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResTable.class", "size": 4098, "crc": 1652287505}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$StringPoolChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$StringPoolChunk.class", "size": 5404, "crc": 1890240208}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$StringStyledSpan.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$StringStyledSpan.class", "size": 1188, "crc": -1224773106}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$TypeChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$TypeChunk.class", "size": 3508, "crc": -1370746935}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$TypeSpecChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$TypeSpecChunk.class", "size": 3272, "crc": -1406842078}, {"key": "com/google/android/material/color/ColorResourcesTableCreator.class", "name": "com/google/android/material/color/ColorResourcesTableCreator.class", "size": 7626, "crc": 563768473}, {"key": "com/google/android/material/color/ColorRoles.class", "name": "com/google/android/material/color/ColorRoles.class", "size": 1049, "crc": 11812506}, {"key": "com/google/android/material/color/DynamicColors$1.class", "name": "com/google/android/material/color/DynamicColors$1.class", "size": 637, "crc": 840017040}, {"key": "com/google/android/material/color/DynamicColors$2.class", "name": "com/google/android/material/color/DynamicColors$2.class", "size": 1404, "crc": 920492411}, {"key": "com/google/android/material/color/DynamicColors$DeviceSupportCondition.class", "name": "com/google/android/material/color/DynamicColors$DeviceSupportCondition.class", "size": 301, "crc": -88210784}, {"key": "com/google/android/material/color/DynamicColors$DynamicColorsActivityLifecycleCallbacks.class", "name": "com/google/android/material/color/DynamicColors$DynamicColorsActivityLifecycleCallbacks.class", "size": 2136, "crc": 165637064}, {"key": "com/google/android/material/color/DynamicColors$OnAppliedCallback.class", "name": "com/google/android/material/color/DynamicColors$OnAppliedCallback.class", "size": 395, "crc": 611766118}, {"key": "com/google/android/material/color/DynamicColors$Precondition.class", "name": "com/google/android/material/color/DynamicColors$Precondition.class", "size": 440, "crc": 461150021}, {"key": "com/google/android/material/color/DynamicColors.class", "name": "com/google/android/material/color/DynamicColors.class", "size": 10569, "crc": -1329206293}, {"key": "com/google/android/material/color/DynamicColorsOptions$1.class", "name": "com/google/android/material/color/DynamicColorsOptions$1.class", "size": 888, "crc": 2114429903}, {"key": "com/google/android/material/color/DynamicColorsOptions$2.class", "name": "com/google/android/material/color/DynamicColorsOptions$2.class", "size": 857, "crc": 1039601200}, {"key": "com/google/android/material/color/DynamicColorsOptions$Builder.class", "name": "com/google/android/material/color/DynamicColorsOptions$Builder.class", "size": 4022, "crc": 1166209039}, {"key": "com/google/android/material/color/DynamicColorsOptions.class", "name": "com/google/android/material/color/DynamicColorsOptions.class", "size": 4120, "crc": -1168734611}, {"key": "com/google/android/material/color/HarmonizedColorAttributes.class", "name": "com/google/android/material/color/HarmonizedColorAttributes.class", "size": 2092, "crc": -2044864617}, {"key": "com/google/android/material/color/HarmonizedColors.class", "name": "com/google/android/material/color/HarmonizedColors.class", "size": 5676, "crc": -1841757095}, {"key": "com/google/android/material/color/HarmonizedColorsOptions$1.class", "name": "com/google/android/material/color/HarmonizedColorsOptions$1.class", "size": 285, "crc": -1585092742}, {"key": "com/google/android/material/color/HarmonizedColorsOptions$Builder.class", "name": "com/google/android/material/color/HarmonizedColorsOptions$Builder.class", "size": 2667, "crc": 707695864}, {"key": "com/google/android/material/color/HarmonizedColorsOptions.class", "name": "com/google/android/material/color/HarmonizedColorsOptions.class", "size": 2921, "crc": 225117268}, {"key": "com/google/android/material/color/MaterialColorUtilitiesHelper.class", "name": "com/google/android/material/color/MaterialColorUtilitiesHelper.class", "size": 7698, "crc": 112392458}, {"key": "com/google/android/material/color/MaterialColors.class", "name": "com/google/android/material/color/MaterialColors.class", "size": 8110, "crc": -408382730}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$1.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$1.class", "size": 327, "crc": -290460067}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$ResourcesLoaderColorResourcesOverrideSingleton.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$ResourcesLoaderColorResourcesOverrideSingleton.class", "size": 1132, "crc": 1855955194}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride.class", "size": 2854, "crc": -1938164950}, {"key": "com/google/android/material/color/ResourcesLoaderUtils.class", "name": "com/google/android/material/color/ResourcesLoaderUtils.class", "size": 1604, "crc": -20830012}, {"key": "com/google/android/material/color/ThemeUtils.class", "name": "com/google/android/material/color/ThemeUtils.class", "size": 1899, "crc": 1860576408}, {"key": "com/google/android/material/color/utilities/Blend.class", "name": "com/google/android/material/color/utilities/Blend.class", "size": 2593, "crc": -580412311}, {"key": "com/google/android/material/color/utilities/Cam16.class", "name": "com/google/android/material/color/utilities/Cam16.class", "size": 8926, "crc": 1128825413}, {"key": "com/google/android/material/color/utilities/ColorUtils.class", "name": "com/google/android/material/color/utilities/ColorUtils.class", "size": 5068, "crc": -969473988}, {"key": "com/google/android/material/color/utilities/Contrast.class", "name": "com/google/android/material/color/utilities/Contrast.class", "size": 2434, "crc": -1882386822}, {"key": "com/google/android/material/color/utilities/CorePalette.class", "name": "com/google/android/material/color/utilities/CorePalette.class", "size": 2001, "crc": -1170056290}, {"key": "com/google/android/material/color/utilities/DislikeAnalyzer.class", "name": "com/google/android/material/color/utilities/DislikeAnalyzer.class", "size": 1607, "crc": 1214754928}, {"key": "com/google/android/material/color/utilities/DynamicColor$1.class", "name": "com/google/android/material/color/utilities/DynamicColor$1.class", "size": 921, "crc": -528918694}, {"key": "com/google/android/material/color/utilities/DynamicColor.class", "name": "com/google/android/material/color/utilities/DynamicColor.class", "size": 23142, "crc": 1377350108}, {"key": "com/google/android/material/color/utilities/DynamicScheme.class", "name": "com/google/android/material/color/utilities/DynamicScheme.class", "size": 2593, "crc": 1752951361}, {"key": "com/google/android/material/color/utilities/Hct.class", "name": "com/google/android/material/color/utilities/Hct.class", "size": 3040, "crc": 554745171}, {"key": "com/google/android/material/color/utilities/HctSolver.class", "name": "com/google/android/material/color/utilities/HctSolver.class", "size": 12823, "crc": -465651680}, {"key": "com/google/android/material/color/utilities/MaterialDynamicColors.class", "name": "com/google/android/material/color/utilities/MaterialDynamicColors.class", "size": 35025, "crc": 654009775}, {"key": "com/google/android/material/color/utilities/MathUtils.class", "name": "com/google/android/material/color/utilities/MathUtils.class", "size": 2083, "crc": 697999511}, {"key": "com/google/android/material/color/utilities/PointProvider.class", "name": "com/google/android/material/color/utilities/PointProvider.class", "size": 499, "crc": 890141672}, {"key": "com/google/android/material/color/utilities/PointProviderLab.class", "name": "com/google/android/material/color/utilities/PointProviderLab.class", "size": 1266, "crc": -**********}, {"key": "com/google/android/material/color/utilities/Quantizer.class", "name": "com/google/android/material/color/utilities/Quantizer.class", "size": 500, "crc": -**********}, {"key": "com/google/android/material/color/utilities/QuantizerCelebi.class", "name": "com/google/android/material/color/utilities/QuantizerCelebi.class", "size": 2030, "crc": **********}, {"key": "com/google/android/material/color/utilities/QuantizerMap.class", "name": "com/google/android/material/color/utilities/QuantizerMap.class", "size": 1909, "crc": 445175726}, {"key": "com/google/android/material/color/utilities/QuantizerResult.class", "name": "com/google/android/material/color/utilities/QuantizerResult.class", "size": 912, "crc": -644167195}, {"key": "com/google/android/material/color/utilities/QuantizerWsmeans$Distance.class", "name": "com/google/android/material/color/utilities/QuantizerWsmeans$Distance.class", "size": 1127, "crc": 967597522}, {"key": "com/google/android/material/color/utilities/QuantizerWsmeans.class", "name": "com/google/android/material/color/utilities/QuantizerWsmeans.class", "size": 5536, "crc": -735988487}, {"key": "com/google/android/material/color/utilities/QuantizerWu$1.class", "name": "com/google/android/material/color/utilities/QuantizerWu$1.class", "size": 960, "crc": -986541561}, {"key": "com/google/android/material/color/utilities/QuantizerWu$Box.class", "name": "com/google/android/material/color/utilities/QuantizerWu$Box.class", "size": 960, "crc": -1456146842}, {"key": "com/google/android/material/color/utilities/QuantizerWu$CreateBoxesResult.class", "name": "com/google/android/material/color/utilities/QuantizerWu$CreateBoxesResult.class", "size": 588, "crc": -915155305}, {"key": "com/google/android/material/color/utilities/QuantizerWu$Direction.class", "name": "com/google/android/material/color/utilities/QuantizerWu$Direction.class", "size": 1362, "crc": -1645583997}, {"key": "com/google/android/material/color/utilities/QuantizerWu$MaximizeResult.class", "name": "com/google/android/material/color/utilities/QuantizerWu$MaximizeResult.class", "size": 615, "crc": 1009403002}, {"key": "com/google/android/material/color/utilities/QuantizerWu.class", "name": "com/google/android/material/color/utilities/QuantizerWu.class", "size": 11839, "crc": -1996678408}, {"key": "com/google/android/material/color/utilities/Scheme.class", "name": "com/google/android/material/color/utilities/Scheme.class", "size": 15576, "crc": -1781689227}, {"key": "com/google/android/material/color/utilities/SchemeContent.class", "name": "com/google/android/material/color/utilities/SchemeContent.class", "size": 2443, "crc": -1562357225}, {"key": "com/google/android/material/color/utilities/SchemeExpressive.class", "name": "com/google/android/material/color/utilities/SchemeExpressive.class", "size": 2489, "crc": 628429951}, {"key": "com/google/android/material/color/utilities/SchemeFidelity.class", "name": "com/google/android/material/color/utilities/SchemeFidelity.class", "size": 2393, "crc": 1417262220}, {"key": "com/google/android/material/color/utilities/SchemeFruitSalad.class", "name": "com/google/android/material/color/utilities/SchemeFruitSalad.class", "size": 1876, "crc": 370260006}, {"key": "com/google/android/material/color/utilities/SchemeMonochrome.class", "name": "com/google/android/material/color/utilities/SchemeMonochrome.class", "size": 1698, "crc": -948787805}, {"key": "com/google/android/material/color/utilities/SchemeNeutral.class", "name": "com/google/android/material/color/utilities/SchemeNeutral.class", "size": 1732, "crc": 744572723}, {"key": "com/google/android/material/color/utilities/SchemeRainbow.class", "name": "com/google/android/material/color/utilities/SchemeRainbow.class", "size": 1839, "crc": -118277544}, {"key": "com/google/android/material/color/utilities/SchemeTonalSpot.class", "name": "com/google/android/material/color/utilities/SchemeTonalSpot.class", "size": 1870, "crc": 434878037}, {"key": "com/google/android/material/color/utilities/SchemeVibrant.class", "name": "com/google/android/material/color/utilities/SchemeVibrant.class", "size": 2335, "crc": 1342693300}, {"key": "com/google/android/material/color/utilities/Score$ScoredComparator.class", "name": "com/google/android/material/color/utilities/Score$ScoredComparator.class", "size": 1197, "crc": -846161195}, {"key": "com/google/android/material/color/utilities/Score$ScoredHCT.class", "name": "com/google/android/material/color/utilities/Score$ScoredHCT.class", "size": 650, "crc": -366390893}, {"key": "com/google/android/material/color/utilities/Score.class", "name": "com/google/android/material/color/utilities/Score.class", "size": 5518, "crc": -186787254}, {"key": "com/google/android/material/color/utilities/TemperatureCache.class", "name": "com/google/android/material/color/utilities/TemperatureCache.class", "size": 8794, "crc": -1745023675}, {"key": "com/google/android/material/color/utilities/TonalPalette.class", "name": "com/google/android/material/color/utilities/TonalPalette.class", "size": 3368, "crc": 1449494683}, {"key": "com/google/android/material/color/utilities/ToneDeltaConstraint.class", "name": "com/google/android/material/color/utilities/ToneDeltaConstraint.class", "size": 1042, "crc": 849720621}, {"key": "com/google/android/material/color/utilities/TonePolarity.class", "name": "com/google/android/material/color/utilities/TonePolarity.class", "size": 1488, "crc": -1898979031}, {"key": "com/google/android/material/color/utilities/Variant.class", "name": "com/google/android/material/color/utilities/Variant.class", "size": 1793, "crc": 669510824}, {"key": "com/google/android/material/color/utilities/ViewingConditions.class", "name": "com/google/android/material/color/utilities/ViewingConditions.class", "size": 4076, "crc": -863969166}, {"key": "com/google/android/material/datepicker/CalendarConstraints$1.class", "name": "com/google/android/material/datepicker/CalendarConstraints$1.class", "size": 2420, "crc": 31686643}, {"key": "com/google/android/material/datepicker/CalendarConstraints$Builder.class", "name": "com/google/android/material/datepicker/CalendarConstraints$Builder.class", "size": 4540, "crc": 1189498540}, {"key": "com/google/android/material/datepicker/CalendarConstraints$DateValidator.class", "name": "com/google/android/material/datepicker/CalendarConstraints$DateValidator.class", "size": 337, "crc": -2116496878}, {"key": "com/google/android/material/datepicker/CalendarConstraints.class", "name": "com/google/android/material/datepicker/CalendarConstraints.class", "size": 6728, "crc": 943058206}, {"key": "com/google/android/material/datepicker/CalendarItemStyle.class", "name": "com/google/android/material/datepicker/CalendarItemStyle.class", "size": 6186, "crc": 108495919}, {"key": "com/google/android/material/datepicker/CalendarStyle.class", "name": "com/google/android/material/datepicker/CalendarStyle.class", "size": 2972, "crc": -581185952}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$1.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$1.class", "size": 1723, "crc": -774395914}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$2.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$2.class", "size": 1723, "crc": -512470639}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$3.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$3.class", "size": 2854, "crc": 1606820813}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$Operator.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$Operator.class", "size": 716, "crc": 1146446419}, {"key": "com/google/android/material/datepicker/CompositeDateValidator.class", "name": "com/google/android/material/datepicker/CompositeDateValidator.class", "size": 4138, "crc": -1323399785}, {"key": "com/google/android/material/datepicker/DateFormatTextWatcher.class", "name": "com/google/android/material/datepicker/DateFormatTextWatcher.class", "size": 6645, "crc": 2050175255}, {"key": "com/google/android/material/datepicker/DateSelector.class", "name": "com/google/android/material/datepicker/DateSelector.class", "size": 4107, "crc": 226901802}, {"key": "com/google/android/material/datepicker/DateStrings.class", "name": "com/google/android/material/datepicker/DateStrings.class", "size": 6053, "crc": 196076813}, {"key": "com/google/android/material/datepicker/DateValidatorPointBackward$1.class", "name": "com/google/android/material/datepicker/DateValidatorPointBackward$1.class", "size": 1647, "crc": 1060827714}, {"key": "com/google/android/material/datepicker/DateValidatorPointBackward.class", "name": "com/google/android/material/datepicker/DateValidatorPointBackward.class", "size": 2788, "crc": -2120730260}, {"key": "com/google/android/material/datepicker/DateValidatorPointForward$1.class", "name": "com/google/android/material/datepicker/DateValidatorPointForward$1.class", "size": 1639, "crc": -1220939102}, {"key": "com/google/android/material/datepicker/DateValidatorPointForward.class", "name": "com/google/android/material/datepicker/DateValidatorPointForward.class", "size": 2777, "crc": -183556569}, {"key": "com/google/android/material/datepicker/DayViewDecorator.class", "name": "com/google/android/material/datepicker/DayViewDecorator.class", "size": 2259, "crc": 1441849470}, {"key": "com/google/android/material/datepicker/DaysOfWeekAdapter.class", "name": "com/google/android/material/datepicker/DaysOfWeekAdapter.class", "size": 3868, "crc": -1093182197}, {"key": "com/google/android/material/datepicker/MaterialCalendar$1.class", "name": "com/google/android/material/datepicker/MaterialCalendar$1.class", "size": 1406, "crc": -948658164}, {"key": "com/google/android/material/datepicker/MaterialCalendar$10.class", "name": "com/google/android/material/datepicker/MaterialCalendar$10.class", "size": 1718, "crc": 887814089}, {"key": "com/google/android/material/datepicker/MaterialCalendar$11.class", "name": "com/google/android/material/datepicker/MaterialCalendar$11.class", "size": 1106, "crc": -2010992476}, {"key": "com/google/android/material/datepicker/MaterialCalendar$2.class", "name": "com/google/android/material/datepicker/MaterialCalendar$2.class", "size": 1799, "crc": -339315272}, {"key": "com/google/android/material/datepicker/MaterialCalendar$3.class", "name": "com/google/android/material/datepicker/MaterialCalendar$3.class", "size": 2813, "crc": -865900870}, {"key": "com/google/android/material/datepicker/MaterialCalendar$4.class", "name": "com/google/android/material/datepicker/MaterialCalendar$4.class", "size": 1298, "crc": -2108661155}, {"key": "com/google/android/material/datepicker/MaterialCalendar$5.class", "name": "com/google/android/material/datepicker/MaterialCalendar$5.class", "size": 4756, "crc": -368783916}, {"key": "com/google/android/material/datepicker/MaterialCalendar$6.class", "name": "com/google/android/material/datepicker/MaterialCalendar$6.class", "size": 1938, "crc": -323725978}, {"key": "com/google/android/material/datepicker/MaterialCalendar$7.class", "name": "com/google/android/material/datepicker/MaterialCalendar$7.class", "size": 3097, "crc": 1362342222}, {"key": "com/google/android/material/datepicker/MaterialCalendar$8.class", "name": "com/google/android/material/datepicker/MaterialCalendar$8.class", "size": 1086, "crc": -2077611667}, {"key": "com/google/android/material/datepicker/MaterialCalendar$9.class", "name": "com/google/android/material/datepicker/MaterialCalendar$9.class", "size": 2082, "crc": -1408274799}, {"key": "com/google/android/material/datepicker/MaterialCalendar$CalendarSelector.class", "name": "com/google/android/material/datepicker/MaterialCalendar$CalendarSelector.class", "size": 1364, "crc": -2019752869}, {"key": "com/google/android/material/datepicker/MaterialCalendar$OnDayClickListener.class", "name": "com/google/android/material/datepicker/MaterialCalendar$OnDayClickListener.class", "size": 312, "crc": 2089774865}, {"key": "com/google/android/material/datepicker/MaterialCalendar.class", "name": "com/google/android/material/datepicker/MaterialCalendar.class", "size": 18480, "crc": -1901473798}, {"key": "com/google/android/material/datepicker/MaterialCalendarGridView$1.class", "name": "com/google/android/material/datepicker/MaterialCalendarGridView$1.class", "size": 1341, "crc": -1790029630}, {"key": "com/google/android/material/datepicker/MaterialCalendarGridView.class", "name": "com/google/android/material/datepicker/MaterialCalendarGridView.class", "size": 9377, "crc": -712176257}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$1.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$1.class", "size": 1828, "crc": 1210568185}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$2.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$2.class", "size": 1509, "crc": -635021088}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$3.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$3.class", "size": 2075, "crc": 55511415}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$4.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$4.class", "size": 1727, "crc": 1378440041}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$Builder.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$Builder.class", "size": 8537, "crc": -1952628550}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$InputMode.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$InputMode.class", "size": 689, "crc": -2020931348}, {"key": "com/google/android/material/datepicker/MaterialDatePicker.class", "name": "com/google/android/material/datepicker/MaterialDatePicker.class", "size": 26229, "crc": -1497162297}, {"key": "com/google/android/material/datepicker/MaterialPickerOnPositiveButtonClickListener.class", "name": "com/google/android/material/datepicker/MaterialPickerOnPositiveButtonClickListener.class", "size": 342, "crc": -907228660}, {"key": "com/google/android/material/datepicker/MaterialStyledDatePickerDialog.class", "name": "com/google/android/material/datepicker/MaterialStyledDatePickerDialog.class", "size": 4516, "crc": -191459979}, {"key": "com/google/android/material/datepicker/MaterialTextInputPicker$1.class", "name": "com/google/android/material/datepicker/MaterialTextInputPicker$1.class", "size": 1810, "crc": 17850549}, {"key": "com/google/android/material/datepicker/MaterialTextInputPicker.class", "name": "com/google/android/material/datepicker/MaterialTextInputPicker.class", "size": 4962, "crc": 301697917}, {"key": "com/google/android/material/datepicker/Month$1.class", "name": "com/google/android/material/datepicker/Month$1.class", "size": 1531, "crc": 1246212404}, {"key": "com/google/android/material/datepicker/Month.class", "name": "com/google/android/material/datepicker/Month.class", "size": 4876, "crc": 362720274}, {"key": "com/google/android/material/datepicker/MonthAdapter.class", "name": "com/google/android/material/datepicker/MonthAdapter.class", "size": 11558, "crc": 1676258817}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter$1.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter$1.class", "size": 2314, "crc": -2009824566}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter$ViewHolder.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter$ViewHolder.class", "size": 1538, "crc": 209710244}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter.class", "size": 8098, "crc": -1573012645}, {"key": "com/google/android/material/datepicker/OnSelectionChangedListener.class", "name": "com/google/android/material/datepicker/OnSelectionChangedListener.class", "size": 1091, "crc": -122872046}, {"key": "com/google/android/material/datepicker/PickerFragment.class", "name": "com/google/android/material/datepicker/PickerFragment.class", "size": 1726, "crc": -309718898}, {"key": "com/google/android/material/datepicker/RangeDateSelector$1.class", "name": "com/google/android/material/datepicker/RangeDateSelector$1.class", "size": 2643, "crc": -869872026}, {"key": "com/google/android/material/datepicker/RangeDateSelector$2.class", "name": "com/google/android/material/datepicker/RangeDateSelector$2.class", "size": 2643, "crc": 123193243}, {"key": "com/google/android/material/datepicker/RangeDateSelector$3.class", "name": "com/google/android/material/datepicker/RangeDateSelector$3.class", "size": 1923, "crc": -347197638}, {"key": "com/google/android/material/datepicker/RangeDateSelector.class", "name": "com/google/android/material/datepicker/RangeDateSelector.class", "size": 14721, "crc": 207307241}, {"key": "com/google/android/material/datepicker/SingleDateSelector$1.class", "name": "com/google/android/material/datepicker/SingleDateSelector$1.class", "size": 2839, "crc": 2102635337}, {"key": "com/google/android/material/datepicker/SingleDateSelector$2.class", "name": "com/google/android/material/datepicker/SingleDateSelector$2.class", "size": 1881, "crc": -187096758}, {"key": "com/google/android/material/datepicker/SingleDateSelector.class", "name": "com/google/android/material/datepicker/SingleDateSelector.class", "size": 9497, "crc": 725370407}, {"key": "com/google/android/material/datepicker/SmoothCalendarLayoutManager$1.class", "name": "com/google/android/material/datepicker/SmoothCalendarLayoutManager$1.class", "size": 1332, "crc": -120045137}, {"key": "com/google/android/material/datepicker/SmoothCalendarLayoutManager.class", "name": "com/google/android/material/datepicker/SmoothCalendarLayoutManager.class", "size": 1749, "crc": 945728180}, {"key": "com/google/android/material/datepicker/TimeSource.class", "name": "com/google/android/material/datepicker/TimeSource.class", "size": 1757, "crc": -2102898150}, {"key": "com/google/android/material/datepicker/UtcDates.class", "name": "com/google/android/material/datepicker/UtcDates.class", "size": 8710, "crc": 958252498}, {"key": "com/google/android/material/datepicker/YearGridAdapter$1.class", "name": "com/google/android/material/datepicker/YearGridAdapter$1.class", "size": 2315, "crc": -28366849}, {"key": "com/google/android/material/datepicker/YearGridAdapter$ViewHolder.class", "name": "com/google/android/material/datepicker/YearGridAdapter$ViewHolder.class", "size": 703, "crc": 1591464541}, {"key": "com/google/android/material/datepicker/YearGridAdapter.class", "name": "com/google/android/material/datepicker/YearGridAdapter.class", "size": 5995, "crc": -135088531}, {"key": "com/google/android/material/dialog/InsetDialogOnTouchListener.class", "name": "com/google/android/material/dialog/InsetDialogOnTouchListener.class", "size": 2842, "crc": 1026472968}, {"key": "com/google/android/material/dialog/MaterialAlertDialogBuilder.class", "name": "com/google/android/material/dialog/MaterialAlertDialogBuilder.class", "size": 22553, "crc": -633612917}, {"key": "com/google/android/material/dialog/MaterialDialogs.class", "name": "com/google/android/material/dialog/MaterialDialogs.class", "size": 3534, "crc": 1767109174}, {"key": "com/google/android/material/divider/MaterialDivider.class", "name": "com/google/android/material/divider/MaterialDivider.class", "size": 6130, "crc": 1920468292}, {"key": "com/google/android/material/divider/MaterialDividerItemDecoration.class", "name": "com/google/android/material/divider/MaterialDividerItemDecoration.class", "size": 10462, "crc": -**********}, {"key": "com/google/android/material/drawable/DrawableUtils.class", "name": "com/google/android/material/drawable/DrawableUtils.class", "size": 10346, "crc": -**********}, {"key": "com/google/android/material/drawable/ScaledDrawableWrapper.class", "name": "com/google/android/material/drawable/ScaledDrawableWrapper.class", "size": 1170, "crc": -890813082}, {"key": "com/google/android/material/elevation/ElevationOverlayProvider.class", "name": "com/google/android/material/elevation/ElevationOverlayProvider.class", "size": 4507, "crc": -466869873}, {"key": "com/google/android/material/elevation/SurfaceColors.class", "name": "com/google/android/material/elevation/SurfaceColors.class", "size": 2933, "crc": 141767193}, {"key": "com/google/android/material/expandable/ExpandableTransformationWidget.class", "name": "com/google/android/material/expandable/ExpandableTransformationWidget.class", "size": 454, "crc": **********}, {"key": "com/google/android/material/expandable/ExpandableWidget.class", "name": "com/google/android/material/expandable/ExpandableWidget.class", "size": 208, "crc": **********}, {"key": "com/google/android/material/expandable/ExpandableWidgetHelper.class", "name": "com/google/android/material/expandable/ExpandableWidgetHelper.class", "size": 2310, "crc": -265228842}, {"key": "com/google/android/material/floatingactionbutton/AnimatorTracker.class", "name": "com/google/android/material/floatingactionbutton/AnimatorTracker.class", "size": 917, "crc": -**********}, {"key": "com/google/android/material/floatingactionbutton/BaseMotionStrategy$1.class", "name": "com/google/android/material/floatingactionbutton/BaseMotionStrategy$1.class", "size": 3284, "crc": -169900196}, {"key": "com/google/android/material/floatingactionbutton/BaseMotionStrategy.class", "name": "com/google/android/material/floatingactionbutton/BaseMotionStrategy.class", "size": 5665, "crc": -1043463160}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable$1.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable$1.class", "size": 288, "crc": 1832180925}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable$BorderState.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable$BorderState.class", "size": 1441, "crc": -2056651243}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable.class", "size": 8304, "crc": -1827143812}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$1.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$1.class", "size": 1573, "crc": -1617031330}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$2.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$2.class", "size": 1815, "crc": -993841322}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$3.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$3.class", "size": 3215, "crc": -7835969}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$4.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$4.class", "size": 2397, "crc": -1444618606}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$5.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$5.class", "size": 2149, "crc": -379395322}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$6.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$6.class", "size": 1924, "crc": -1423932182}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$7.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$7.class", "size": 1925, "crc": 852706172}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$8.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$8.class", "size": 1955, "crc": 758643058}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$9.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$9.class", "size": 1955, "crc": 1885050733}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ChangeSizeStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ChangeSizeStrategy.class", "size": 6171, "crc": -1251888518}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior.class", "size": 9377, "crc": 100918096}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$HideStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$HideStrategy.class", "size": 2821, "crc": 354124843}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$OnChangedCallback.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$OnChangedCallback.class", "size": 1073, "crc": -36522146}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ShowStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ShowStrategy.class", "size": 2757, "crc": 1534101782}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$Size.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$Size.class", "size": 567, "crc": -1288779367}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton.class", "size": 19999, "crc": -1354307716}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$1.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$1.class", "size": 1846, "crc": -804126910}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$BaseBehavior.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$BaseBehavior.class", "size": 9007, "crc": -455064824}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$Behavior.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$Behavior.class", "size": 2835, "crc": -1011396122}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$OnVisibilityChangedListener.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$OnVisibilityChangedListener.class", "size": 887, "crc": -1609077181}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$ShadowDelegateImpl.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$ShadowDelegateImpl.class", "size": 1941, "crc": 251084415}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$Size.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$Size.class", "size": 705, "crc": 1477357631}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$TransformationCallbackWrapper.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$TransformationCallbackWrapper.class", "size": 2563, "crc": 1878293433}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton.class", "size": 28270, "crc": -803235283}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$1.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$1.class", "size": 2463, "crc": -512394948}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$2.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$2.class", "size": 2232, "crc": -385125615}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$3.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$3.class", "size": 1544, "crc": -1366882125}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$4.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$4.class", "size": 2467, "crc": 1911355500}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$5.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$5.class", "size": 1674, "crc": -606119974}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$6.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$6.class", "size": 1041, "crc": 27541750}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$DisabledElevationAnimation.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$DisabledElevationAnimation.class", "size": 1213, "crc": -1663603580}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ElevateToHoveredFocusedTranslationZAnimation.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ElevateToHoveredFocusedTranslationZAnimation.class", "size": 1346, "crc": 445320243}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ElevateToPressedTranslationZAnimation.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ElevateToPressedTranslationZAnimation.class", "size": 1318, "crc": 554155955}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalTransformationCallback.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalTransformationCallback.class", "size": 414, "crc": -1051265116}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalVisibilityChangedListener.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalVisibilityChangedListener.class", "size": 401, "crc": -949649259}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ResetElevationAnimation.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ResetElevationAnimation.class", "size": 1236, "crc": 185411194}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ShadowAnimatorImpl.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$ShadowAnimatorImpl.class", "size": 2323, "crc": -389670055}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl.class", "size": 26715, "crc": -793281255}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImplLollipop$AlwaysStatefulMaterialShapeDrawable.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImplLollipop$AlwaysStatefulMaterialShapeDrawable.class", "size": 916, "crc": **********}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImplLollipop.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImplLollipop.class", "size": 9443, "crc": 227852755}, {"key": "com/google/android/material/floatingactionbutton/MotionStrategy.class", "name": "com/google/android/material/floatingactionbutton/MotionStrategy.class", "size": 1583, "crc": 651843579}, {"key": "com/google/android/material/imageview/ShapeableImageView$OutlineProvider.class", "name": "com/google/android/material/imageview/ShapeableImageView$OutlineProvider.class", "size": 2084, "crc": -94419637}, {"key": "com/google/android/material/imageview/ShapeableImageView.class", "name": "com/google/android/material/imageview/ShapeableImageView.class", "size": 13406, "crc": -954194182}, {"key": "com/google/android/material/internal/BaselineLayout.class", "name": "com/google/android/material/internal/BaselineLayout.class", "size": 2984, "crc": -**********}, {"key": "com/google/android/material/internal/CheckableGroup$1.class", "name": "com/google/android/material/internal/CheckableGroup$1.class", "size": 1926, "crc": -489003325}, {"key": "com/google/android/material/internal/CheckableGroup$OnCheckedStateChangeListener.class", "name": "com/google/android/material/internal/CheckableGroup$OnCheckedStateChangeListener.class", "size": 493, "crc": -**********}, {"key": "com/google/android/material/internal/CheckableGroup.class", "name": "com/google/android/material/internal/CheckableGroup.class", "size": 7647, "crc": 303469690}, {"key": "com/google/android/material/internal/CheckableImageButton$1.class", "name": "com/google/android/material/internal/CheckableImageButton$1.class", "size": 1704, "crc": -1894983765}, {"key": "com/google/android/material/internal/CheckableImageButton$SavedState$1.class", "name": "com/google/android/material/internal/CheckableImageButton$SavedState$1.class", "size": 2121, "crc": -149698246}, {"key": "com/google/android/material/internal/CheckableImageButton$SavedState.class", "name": "com/google/android/material/internal/CheckableImageButton$SavedState.class", "size": 1855, "crc": 1245371590}, {"key": "com/google/android/material/internal/CheckableImageButton.class", "name": "com/google/android/material/internal/CheckableImageButton.class", "size": 3730, "crc": 1336007477}, {"key": "com/google/android/material/internal/ClippableRoundedCornerLayout.class", "name": "com/google/android/material/internal/ClippableRoundedCornerLayout.class", "size": 3251, "crc": -646141447}, {"key": "com/google/android/material/internal/CollapsingTextHelper$1.class", "name": "com/google/android/material/internal/CollapsingTextHelper$1.class", "size": 1049, "crc": -950815152}, {"key": "com/google/android/material/internal/CollapsingTextHelper$2.class", "name": "com/google/android/material/internal/CollapsingTextHelper$2.class", "size": 1047, "crc": 1512971991}, {"key": "com/google/android/material/internal/CollapsingTextHelper.class", "name": "com/google/android/material/internal/CollapsingTextHelper.class", "size": 31129, "crc": -1647145233}, {"key": "com/google/android/material/internal/ContextUtils.class", "name": "com/google/android/material/internal/ContextUtils.class", "size": 1003, "crc": 1312988358}, {"key": "com/google/android/material/internal/DescendantOffsetUtils.class", "name": "com/google/android/material/internal/DescendantOffsetUtils.class", "size": 2974, "crc": -505590132}, {"key": "com/google/android/material/internal/EdgeToEdgeUtils.class", "name": "com/google/android/material/internal/EdgeToEdgeUtils.class", "size": 4085, "crc": 337736068}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper$1.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper$1.class", "size": 1140, "crc": -1124137820}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper$2.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper$2.class", "size": 1141, "crc": -328102156}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper.class", "size": 8773, "crc": 1260609299}, {"key": "com/google/android/material/internal/Experimental.class", "name": "com/google/android/material/internal/Experimental.class", "size": 664, "crc": -1282203155}, {"key": "com/google/android/material/internal/FadeThroughDrawable.class", "name": "com/google/android/material/internal/FadeThroughDrawable.class", "size": 3512, "crc": -1915967416}, {"key": "com/google/android/material/internal/FadeThroughUpdateListener.class", "name": "com/google/android/material/internal/FadeThroughUpdateListener.class", "size": 1721, "crc": 912778878}, {"key": "com/google/android/material/internal/FadeThroughUtils.class", "name": "com/google/android/material/internal/FadeThroughUtils.class", "size": 743, "crc": -602612030}, {"key": "com/google/android/material/internal/FlowLayout.class", "name": "com/google/android/material/internal/FlowLayout.class", "size": 7085, "crc": 723258360}, {"key": "com/google/android/material/internal/ForegroundLinearLayout.class", "name": "com/google/android/material/internal/ForegroundLinearLayout.class", "size": 6019, "crc": -1044356097}, {"key": "com/google/android/material/internal/ManufacturerUtils.class", "name": "com/google/android/material/internal/ManufacturerUtils.class", "size": 1565, "crc": 26979073}, {"key": "com/google/android/material/internal/MaterialCheckable$OnCheckedChangeListener.class", "name": "com/google/android/material/internal/MaterialCheckable$OnCheckedChangeListener.class", "size": 426, "crc": -1372626070}, {"key": "com/google/android/material/internal/MaterialCheckable.class", "name": "com/google/android/material/internal/MaterialCheckable.class", "size": 1083, "crc": 1797343001}, {"key": "com/google/android/material/internal/MultiViewUpdateListener$Listener.class", "name": "com/google/android/material/internal/MultiViewUpdateListener$Listener.class", "size": 457, "crc": 2135629768}, {"key": "com/google/android/material/internal/MultiViewUpdateListener.class", "name": "com/google/android/material/internal/MultiViewUpdateListener.class", "size": 4891, "crc": 1961113694}, {"key": "com/google/android/material/internal/NavigationMenu.class", "name": "com/google/android/material/internal/NavigationMenu.class", "size": 1620, "crc": 792458063}, {"key": "com/google/android/material/internal/NavigationMenuItemView$1.class", "name": "com/google/android/material/internal/NavigationMenuItemView$1.class", "size": 1242, "crc": -1435026808}, {"key": "com/google/android/material/internal/NavigationMenuItemView.class", "name": "com/google/android/material/internal/NavigationMenuItemView.class", "size": 11564, "crc": 1803851026}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$1.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$1.class", "size": 2068, "crc": -1575223197}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$HeaderViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$HeaderViewHolder.class", "size": 661, "crc": 452763712}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter$1.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter$1.class", "size": 2048, "crc": -1362078024}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter.class", "size": 14272, "crc": 920383948}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuHeaderItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuHeaderItem.class", "size": 658, "crc": -1814135824}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuItem.class", "size": 301, "crc": -1275225037}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuSeparatorItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuSeparatorItem.class", "size": 949, "crc": 1927436400}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuTextItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuTextItem.class", "size": 958, "crc": -38029603}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuViewAccessibilityDelegate.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuViewAccessibilityDelegate.class", "size": 2024, "crc": 387951812}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NormalViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NormalViewHolder.class", "size": 1417, "crc": 2123251986}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$SeparatorViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$SeparatorViewHolder.class", "size": 1125, "crc": -1808779974}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$SubheaderViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$SubheaderViewHolder.class", "size": 1125, "crc": 1883071399}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$ViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$ViewHolder.class", "size": 658, "crc": -221852651}, {"key": "com/google/android/material/internal/NavigationMenuPresenter.class", "name": "com/google/android/material/internal/NavigationMenuPresenter.class", "size": 15173, "crc": 1468302909}, {"key": "com/google/android/material/internal/NavigationMenuView.class", "name": "com/google/android/material/internal/NavigationMenuView.class", "size": 1704, "crc": 289446410}, {"key": "com/google/android/material/internal/NavigationSubMenu.class", "name": "com/google/android/material/internal/NavigationSubMenu.class", "size": 1323, "crc": 1002601535}, {"key": "com/google/android/material/internal/ParcelableSparseArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseArray$1.class", "size": 2005, "crc": 2069718049}, {"key": "com/google/android/material/internal/ParcelableSparseArray.class", "name": "com/google/android/material/internal/ParcelableSparseArray.class", "size": 2535, "crc": 1552414409}, {"key": "com/google/android/material/internal/ParcelableSparseBooleanArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseBooleanArray$1.class", "size": 1977, "crc": 1248227190}, {"key": "com/google/android/material/internal/ParcelableSparseBooleanArray.class", "name": "com/google/android/material/internal/ParcelableSparseBooleanArray.class", "size": 2225, "crc": -1052688207}, {"key": "com/google/android/material/internal/ParcelableSparseIntArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseIntArray$1.class", "size": 1900, "crc": 1951222371}, {"key": "com/google/android/material/internal/ParcelableSparseIntArray.class", "name": "com/google/android/material/internal/ParcelableSparseIntArray.class", "size": 2132, "crc": -372324255}, {"key": "com/google/android/material/internal/RectEvaluator.class", "name": "com/google/android/material/internal/RectEvaluator.class", "size": 1612, "crc": -518082618}, {"key": "com/google/android/material/internal/ReversableAnimatedValueInterpolator.class", "name": "com/google/android/material/internal/ReversableAnimatedValueInterpolator.class", "size": 1331, "crc": 583226529}, {"key": "com/google/android/material/internal/ScrimInsetsFrameLayout$1.class", "name": "com/google/android/material/internal/ScrimInsetsFrameLayout$1.class", "size": 2054, "crc": 1503226292}, {"key": "com/google/android/material/internal/ScrimInsetsFrameLayout.class", "name": "com/google/android/material/internal/ScrimInsetsFrameLayout.class", "size": 4933, "crc": 1618933509}, {"key": "com/google/android/material/internal/StateListAnimator$1.class", "name": "com/google/android/material/internal/StateListAnimator$1.class", "size": 937, "crc": 1967090272}, {"key": "com/google/android/material/internal/StateListAnimator$Tuple.class", "name": "com/google/android/material/internal/StateListAnimator$Tuple.class", "size": 640, "crc": 1941552211}, {"key": "com/google/android/material/internal/StateListAnimator.class", "name": "com/google/android/material/internal/StateListAnimator.class", "size": 2892, "crc": 2127258634}, {"key": "com/google/android/material/internal/StaticLayoutBuilderCompat$StaticLayoutBuilderCompatException.class", "name": "com/google/android/material/internal/StaticLayoutBuilderCompat$StaticLayoutBuilderCompatException.class", "size": 967, "crc": -86159115}, {"key": "com/google/android/material/internal/StaticLayoutBuilderCompat.class", "name": "com/google/android/material/internal/StaticLayoutBuilderCompat.class", "size": 8918, "crc": -14588564}, {"key": "com/google/android/material/internal/StaticLayoutBuilderConfigurer.class", "name": "com/google/android/material/internal/StaticLayoutBuilderConfigurer.class", "size": 678, "crc": 750335035}, {"key": "com/google/android/material/internal/TextDrawableHelper$1.class", "name": "com/google/android/material/internal/TextDrawableHelper$1.class", "size": 1784, "crc": 314309851}, {"key": "com/google/android/material/internal/TextDrawableHelper$TextDrawableDelegate.class", "name": "com/google/android/material/internal/TextDrawableHelper$TextDrawableDelegate.class", "size": 455, "crc": -986538089}, {"key": "com/google/android/material/internal/TextDrawableHelper.class", "name": "com/google/android/material/internal/TextDrawableHelper.class", "size": 5122, "crc": -2130418062}, {"key": "com/google/android/material/internal/TextScale$1.class", "name": "com/google/android/material/internal/TextScale$1.class", "size": 1475, "crc": -587841023}, {"key": "com/google/android/material/internal/TextScale.class", "name": "com/google/android/material/internal/TextScale.class", "size": 3040, "crc": -293449463}, {"key": "com/google/android/material/internal/TextWatcherAdapter.class", "name": "com/google/android/material/internal/TextWatcherAdapter.class", "size": 1248, "crc": 1986812837}, {"key": "com/google/android/material/internal/ThemeEnforcement.class", "name": "com/google/android/material/internal/ThemeEnforcement.class", "size": 6373, "crc": 1522640527}, {"key": "com/google/android/material/internal/ToolbarUtils$1.class", "name": "com/google/android/material/internal/ToolbarUtils$1.class", "size": 912, "crc": 399594709}, {"key": "com/google/android/material/internal/ToolbarUtils.class", "name": "com/google/android/material/internal/ToolbarUtils.class", "size": 5552, "crc": -624034936}, {"key": "com/google/android/material/internal/TouchObserverFrameLayout.class", "name": "com/google/android/material/internal/TouchObserverFrameLayout.class", "size": 1884, "crc": -1375398046}, {"key": "com/google/android/material/internal/ViewGroupOverlayApi14.class", "name": "com/google/android/material/internal/ViewGroupOverlayApi14.class", "size": 1503, "crc": -66927911}, {"key": "com/google/android/material/internal/ViewGroupOverlayApi18.class", "name": "com/google/android/material/internal/ViewGroupOverlayApi18.class", "size": 1460, "crc": -1639369348}, {"key": "com/google/android/material/internal/ViewGroupOverlayImpl.class", "name": "com/google/android/material/internal/ViewGroupOverlayImpl.class", "size": 371, "crc": -813626591}, {"key": "com/google/android/material/internal/ViewOverlayApi14$OverlayViewGroup.class", "name": "com/google/android/material/internal/ViewOverlayApi14$OverlayViewGroup.class", "size": 6789, "crc": -713248210}, {"key": "com/google/android/material/internal/ViewOverlayApi14.class", "name": "com/google/android/material/internal/ViewOverlayApi14.class", "size": 2175, "crc": -1968999841}, {"key": "com/google/android/material/internal/ViewOverlayApi18.class", "name": "com/google/android/material/internal/ViewOverlayApi18.class", "size": 1146, "crc": -1954010429}, {"key": "com/google/android/material/internal/ViewOverlayImpl.class", "name": "com/google/android/material/internal/ViewOverlayImpl.class", "size": 587, "crc": -1074595663}, {"key": "com/google/android/material/internal/ViewUtils$1.class", "name": "com/google/android/material/internal/ViewUtils$1.class", "size": 2353, "crc": 500447090}, {"key": "com/google/android/material/internal/ViewUtils$2.class", "name": "com/google/android/material/internal/ViewUtils$2.class", "size": 1770, "crc": -1346153363}, {"key": "com/google/android/material/internal/ViewUtils$3.class", "name": "com/google/android/material/internal/ViewUtils$3.class", "size": 1096, "crc": -351163476}, {"key": "com/google/android/material/internal/ViewUtils$OnApplyWindowInsetsListener.class", "name": "com/google/android/material/internal/ViewUtils$OnApplyWindowInsetsListener.class", "size": 567, "crc": 363389601}, {"key": "com/google/android/material/internal/ViewUtils$RelativePadding.class", "name": "com/google/android/material/internal/ViewUtils$RelativePadding.class", "size": 1194, "crc": 416199744}, {"key": "com/google/android/material/internal/ViewUtils.class", "name": "com/google/android/material/internal/ViewUtils.class", "size": 13091, "crc": 1292948841}, {"key": "com/google/android/material/internal/VisibilityAwareImageButton.class", "name": "com/google/android/material/internal/VisibilityAwareImageButton.class", "size": 1654, "crc": 1594645505}, {"key": "com/google/android/material/internal/WindowUtils$Api14Impl.class", "name": "com/google/android/material/internal/WindowUtils$Api14Impl.class", "size": 2443, "crc": 663679526}, {"key": "com/google/android/material/internal/WindowUtils$Api17Impl.class", "name": "com/google/android/material/internal/WindowUtils$Api17Impl.class", "size": 1351, "crc": -608543804}, {"key": "com/google/android/material/internal/WindowUtils$Api30Impl.class", "name": "com/google/android/material/internal/WindowUtils$Api30Impl.class", "size": 1016, "crc": -2118504378}, {"key": "com/google/android/material/internal/WindowUtils.class", "name": "com/google/android/material/internal/WindowUtils.class", "size": 1871, "crc": 66606533}, {"key": "com/google/android/material/internal/package-info.class", "name": "com/google/android/material/internal/package-info.class", "size": 411, "crc": 1279884263}, {"key": "com/google/android/material/materialswitch/MaterialSwitch.class", "name": "com/google/android/material/materialswitch/MaterialSwitch.class", "size": 10144, "crc": -270402753}, {"key": "com/google/android/material/math/MathUtils.class", "name": "com/google/android/material/math/MathUtils.class", "size": 1651, "crc": -381464347}, {"key": "com/google/android/material/motion/MaterialBackAnimationHelper.class", "name": "com/google/android/material/motion/MaterialBackAnimationHelper.class", "size": 3711, "crc": 261228171}, {"key": "com/google/android/material/motion/MaterialBackHandler.class", "name": "com/google/android/material/motion/MaterialBackHandler.class", "size": 681, "crc": 1566665001}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$1.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$1.class", "size": 290, "crc": -1561754080}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api33BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api33BackCallbackDelegate.class", "size": 3393, "crc": -463176803}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate$1.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate$1.class", "size": 2208, "crc": -403055921}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate.class", "size": 1663, "crc": -2101221983}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$BackCallbackDelegate.class", "size": 597, "crc": 970114898}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator.class", "size": 3205, "crc": -1832545640}, {"key": "com/google/android/material/motion/MaterialBottomContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialBottomContainerBackHelper$1.class", "size": 1296, "crc": 1915648551}, {"key": "com/google/android/material/motion/MaterialBottomContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialBottomContainerBackHelper.class", "size": 5802, "crc": 1732466838}, {"key": "com/google/android/material/motion/MaterialMainContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialMainContainerBackHelper$1.class", "size": 1185, "crc": 384929828}, {"key": "com/google/android/material/motion/MaterialMainContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialMainContainerBackHelper.class", "size": 9730, "crc": -124655146}, {"key": "com/google/android/material/motion/MaterialSideContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialSideContainerBackHelper$1.class", "size": 1565, "crc": -1280373017}, {"key": "com/google/android/material/motion/MaterialSideContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialSideContainerBackHelper.class", "size": 7833, "crc": 735856183}, {"key": "com/google/android/material/motion/MotionUtils.class", "name": "com/google/android/material/motion/MotionUtils.class", "size": 4768, "crc": -1602708651}, {"key": "com/google/android/material/navigation/DrawerLayoutUtils$1.class", "name": "com/google/android/material/navigation/DrawerLayoutUtils$1.class", "size": 1304, "crc": -152510634}, {"key": "com/google/android/material/navigation/DrawerLayoutUtils.class", "name": "com/google/android/material/navigation/DrawerLayoutUtils.class", "size": 2950, "crc": 1684789448}, {"key": "com/google/android/material/navigation/NavigationBarItemView$1.class", "name": "com/google/android/material/navigation/NavigationBarItemView$1.class", "size": 1457, "crc": 291644238}, {"key": "com/google/android/material/navigation/NavigationBarItemView$2.class", "name": "com/google/android/material/navigation/NavigationBarItemView$2.class", "size": 868, "crc": 661170446}, {"key": "com/google/android/material/navigation/NavigationBarItemView$3.class", "name": "com/google/android/material/navigation/NavigationBarItemView$3.class", "size": 1321, "crc": 848115491}, {"key": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorTransform.class", "name": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorTransform.class", "size": 2172, "crc": -1778049463}, {"key": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorUnlabeledTransform.class", "name": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorUnlabeledTransform.class", "size": 1137, "crc": -1873140498}, {"key": "com/google/android/material/navigation/NavigationBarItemView.class", "name": "com/google/android/material/navigation/NavigationBarItemView.class", "size": 27840, "crc": 112703393}, {"key": "com/google/android/material/navigation/NavigationBarMenu.class", "name": "com/google/android/material/navigation/NavigationBarMenu.class", "size": 2730, "crc": 1564687941}, {"key": "com/google/android/material/navigation/NavigationBarMenuView$1.class", "name": "com/google/android/material/navigation/NavigationBarMenuView$1.class", "size": 1794, "crc": 303949275}, {"key": "com/google/android/material/navigation/NavigationBarMenuView.class", "name": "com/google/android/material/navigation/NavigationBarMenuView.class", "size": 23731, "crc": -1569019463}, {"key": "com/google/android/material/navigation/NavigationBarPresenter$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationBarPresenter$SavedState$1.class", "size": 1664, "crc": -1704886660}, {"key": "com/google/android/material/navigation/NavigationBarPresenter$SavedState.class", "name": "com/google/android/material/navigation/NavigationBarPresenter$SavedState.class", "size": 2034, "crc": 642166060}, {"key": "com/google/android/material/navigation/NavigationBarPresenter.class", "name": "com/google/android/material/navigation/NavigationBarPresenter.class", "size": 4928, "crc": 302968097}, {"key": "com/google/android/material/navigation/NavigationBarView$1.class", "name": "com/google/android/material/navigation/NavigationBarView$1.class", "size": 2214, "crc": -621016382}, {"key": "com/google/android/material/navigation/NavigationBarView$LabelVisibility.class", "name": "com/google/android/material/navigation/NavigationBarView$LabelVisibility.class", "size": 698, "crc": 1321543811}, {"key": "com/google/android/material/navigation/NavigationBarView$OnItemReselectedListener.class", "name": "com/google/android/material/navigation/NavigationBarView$OnItemReselectedListener.class", "size": 449, "crc": 2019923722}, {"key": "com/google/android/material/navigation/NavigationBarView$OnItemSelectedListener.class", "name": "com/google/android/material/navigation/NavigationBarView$OnItemSelectedListener.class", "size": 443, "crc": 1201546232}, {"key": "com/google/android/material/navigation/NavigationBarView$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationBarView$SavedState$1.class", "size": 2143, "crc": -1292360488}, {"key": "com/google/android/material/navigation/NavigationBarView$SavedState.class", "name": "com/google/android/material/navigation/NavigationBarView$SavedState.class", "size": 2141, "crc": -1135959614}, {"key": "com/google/android/material/navigation/NavigationBarView.class", "name": "com/google/android/material/navigation/NavigationBarView.class", "size": 19869, "crc": 1972420355}, {"key": "com/google/android/material/navigation/NavigationView$1.class", "name": "com/google/android/material/navigation/NavigationView$1.class", "size": 2162, "crc": 342330106}, {"key": "com/google/android/material/navigation/NavigationView$2.class", "name": "com/google/android/material/navigation/NavigationView$2.class", "size": 1573, "crc": 2124130743}, {"key": "com/google/android/material/navigation/NavigationView$3.class", "name": "com/google/android/material/navigation/NavigationView$3.class", "size": 2987, "crc": 211450874}, {"key": "com/google/android/material/navigation/NavigationView$OnNavigationItemSelectedListener.class", "name": "com/google/android/material/navigation/NavigationView$OnNavigationItemSelectedListener.class", "size": 454, "crc": -728516677}, {"key": "com/google/android/material/navigation/NavigationView$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationView$SavedState$1.class", "size": 2116, "crc": 17321471}, {"key": "com/google/android/material/navigation/NavigationView$SavedState.class", "name": "com/google/android/material/navigation/NavigationView$SavedState.class", "size": 1753, "crc": -1478981707}, {"key": "com/google/android/material/navigation/NavigationView.class", "name": "com/google/android/material/navigation/NavigationView.class", "size": 30532, "crc": 538552380}, {"key": "com/google/android/material/navigationrail/NavigationRailItemView.class", "name": "com/google/android/material/navigationrail/NavigationRailItemView.class", "size": 1942, "crc": 973809465}, {"key": "com/google/android/material/navigationrail/NavigationRailMenuView.class", "name": "com/google/android/material/navigationrail/NavigationRailMenuView.class", "size": 5149, "crc": **********}, {"key": "com/google/android/material/navigationrail/NavigationRailView$1.class", "name": "com/google/android/material/navigationrail/NavigationRailView$1.class", "size": 2561, "crc": 33923770}, {"key": "com/google/android/material/navigationrail/NavigationRailView.class", "name": "com/google/android/material/navigationrail/NavigationRailView.class", "size": 10417, "crc": -917115871}, {"key": "com/google/android/material/progressindicator/AnimatorDurationScaleProvider.class", "name": "com/google/android/material/progressindicator/AnimatorDurationScaleProvider.class", "size": 1662, "crc": 529395149}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$1.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$1.class", "size": 889, "crc": -**********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$2.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$2.class", "size": 1012, "crc": **********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$3.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$3.class", "size": 1403, "crc": **********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$4.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$4.class", "size": 1401, "crc": -300928290}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$HideAnimationBehavior.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$HideAnimationBehavior.class", "size": 736, "crc": -375992282}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$ShowAnimationBehavior.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$ShowAnimationBehavior.class", "size": 736, "crc": -1956208374}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator.class", "size": 17999, "crc": -747786409}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicatorSpec.class", "size": 4540, "crc": 667744814}, {"key": "com/google/android/material/progressindicator/CircularDrawingDelegate.class", "name": "com/google/android/material/progressindicator/CircularDrawingDelegate.class", "size": 5627, "crc": 1818024874}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$1.class", "size": 1607, "crc": 1916932570}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$2.class", "size": 1587, "crc": -1287270790}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$3.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$3.class", "size": 1930, "crc": -759560797}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$4.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate$4.class", "size": 2005, "crc": -1299731772}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAnimatorDelegate.class", "size": 8612, "crc": 141431478}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicator$IndicatorDirection.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicator$IndicatorDirection.class", "size": 742, "crc": -241609535}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicator.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicator.class", "size": 4614, "crc": -1871864777}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicatorSpec.class", "size": 3118, "crc": -146024574}, {"key": "com/google/android/material/progressindicator/DeterminateDrawable$1.class", "name": "com/google/android/material/progressindicator/DeterminateDrawable$1.class", "size": 1429, "crc": -1142241600}, {"key": "com/google/android/material/progressindicator/DeterminateDrawable.class", "name": "com/google/android/material/progressindicator/DeterminateDrawable.class", "size": 11818, "crc": 717953467}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$1.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$1.class", "size": 1098, "crc": -995926237}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$2.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$2.class", "size": 1225, "crc": -1051024682}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$3.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$3.class", "size": 1831, "crc": 300141816}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange.class", "size": 10601, "crc": -2086060005}, {"key": "com/google/android/material/progressindicator/DrawingDelegate.class", "name": "com/google/android/material/progressindicator/DrawingDelegate.class", "size": 2153, "crc": -529055510}, {"key": "com/google/android/material/progressindicator/IndeterminateAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/IndeterminateAnimatorDelegate.class", "size": 1822, "crc": 27091176}, {"key": "com/google/android/material/progressindicator/IndeterminateDrawable.class", "name": "com/google/android/material/progressindicator/IndeterminateDrawable.class", "size": 10478, "crc": 1871936729}, {"key": "com/google/android/material/progressindicator/LinearDrawingDelegate.class", "name": "com/google/android/material/progressindicator/LinearDrawingDelegate.class", "size": 4841, "crc": 1674696886}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$1.class", "size": 1815, "crc": 2141468633}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$2.class", "size": 2002, "crc": -1569180557}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate.class", "size": 6557, "crc": 1989651031}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$1.class", "size": 1787, "crc": 985391715}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$2.class", "size": 1623, "crc": -1015442911}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$3.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$3.class", "size": 1984, "crc": 155163141}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate.class", "size": 7765, "crc": -797948801}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator$IndeterminateAnimationType.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator$IndeterminateAnimationType.class", "size": 752, "crc": -399422845}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator$IndicatorDirection.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator$IndicatorDirection.class", "size": 736, "crc": 2136254381}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator.class", "size": 7099, "crc": -243113437}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicatorSpec.class", "size": 2869, "crc": -2102351642}, {"key": "com/google/android/material/radiobutton/MaterialRadioButton.class", "name": "com/google/android/material/radiobutton/MaterialRadioButton.class", "size": 4220, "crc": 601831788}, {"key": "com/google/android/material/resources/CancelableFontCallback$ApplyFont.class", "name": "com/google/android/material/resources/CancelableFontCallback$ApplyFont.class", "size": 331, "crc": 31202101}, {"key": "com/google/android/material/resources/CancelableFontCallback.class", "name": "com/google/android/material/resources/CancelableFontCallback.class", "size": 1715, "crc": 167861274}, {"key": "com/google/android/material/resources/MaterialAttributes.class", "name": "com/google/android/material/resources/MaterialAttributes.class", "size": 4527, "crc": 1670393486}, {"key": "com/google/android/material/resources/MaterialResources.class", "name": "com/google/android/material/resources/MaterialResources.class", "size": 6039, "crc": 2137897904}, {"key": "com/google/android/material/resources/TextAppearance$1.class", "name": "com/google/android/material/resources/TextAppearance$1.class", "size": 2040, "crc": 1543287941}, {"key": "com/google/android/material/resources/TextAppearance$2.class", "name": "com/google/android/material/resources/TextAppearance$2.class", "size": 1747, "crc": -1143749800}, {"key": "com/google/android/material/resources/TextAppearance.class", "name": "com/google/android/material/resources/TextAppearance.class", "size": 10497, "crc": 1499582507}, {"key": "com/google/android/material/resources/TextAppearanceConfig.class", "name": "com/google/android/material/resources/TextAppearanceConfig.class", "size": 657, "crc": -100077187}, {"key": "com/google/android/material/resources/TextAppearanceFontCallback.class", "name": "com/google/android/material/resources/TextAppearanceFontCallback.class", "size": 747, "crc": -2062663114}, {"key": "com/google/android/material/resources/TypefaceUtils.class", "name": "com/google/android/material/resources/TypefaceUtils.class", "size": 2095, "crc": -730816348}, {"key": "com/google/android/material/ripple/RippleDrawableCompat$1.class", "name": "com/google/android/material/ripple/RippleDrawableCompat$1.class", "size": 278, "crc": 180861896}, {"key": "com/google/android/material/ripple/RippleDrawableCompat$RippleDrawableCompatState.class", "name": "com/google/android/material/ripple/RippleDrawableCompat$RippleDrawableCompatState.class", "size": 1951, "crc": 1190617650}, {"key": "com/google/android/material/ripple/RippleDrawableCompat.class", "name": "com/google/android/material/ripple/RippleDrawableCompat.class", "size": 4917, "crc": 1915843444}, {"key": "com/google/android/material/ripple/RippleUtils$RippleUtilsLollipop.class", "name": "com/google/android/material/ripple/RippleUtils$RippleUtilsLollipop.class", "size": 2082, "crc": -1459382042}, {"key": "com/google/android/material/ripple/RippleUtils.class", "name": "com/google/android/material/ripple/RippleUtils.class", "size": 4963, "crc": -234625383}, {"key": "com/google/android/material/search/SearchBar$1.class", "name": "com/google/android/material/search/SearchBar$1.class", "size": 1806, "crc": -260150817}, {"key": "com/google/android/material/search/SearchBar$OnLoadAnimationCallback.class", "name": "com/google/android/material/search/SearchBar$OnLoadAnimationCallback.class", "size": 627, "crc": 828319939}, {"key": "com/google/android/material/search/SearchBar$SavedState$1.class", "name": "com/google/android/material/search/SearchBar$SavedState$1.class", "size": 1815, "crc": 1854585879}, {"key": "com/google/android/material/search/SearchBar$SavedState.class", "name": "com/google/android/material/search/SearchBar$SavedState.class", "size": 1694, "crc": 1424423772}, {"key": "com/google/android/material/search/SearchBar$ScrollingViewBehavior.class", "name": "com/google/android/material/search/SearchBar$ScrollingViewBehavior.class", "size": 2178, "crc": 766021293}, {"key": "com/google/android/material/search/SearchBar.class", "name": "com/google/android/material/search/SearchBar.class", "size": 25784, "crc": -696221415}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$1.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$1.class", "size": 2024, "crc": -512358906}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$2.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$2.class", "size": 1224, "crc": 1131010806}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$3.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$3.class", "size": 1170, "crc": -5652443}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$4.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$4.class", "size": 1468, "crc": -241713733}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$5.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$5.class", "size": 1163, "crc": 1846008794}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$6.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$6.class", "size": 1513, "crc": 520754640}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$OnLoadAnimationInvocation.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$OnLoadAnimationInvocation.class", "size": 565, "crc": -596622580}, {"key": "com/google/android/material/search/SearchBarAnimationHelper.class", "name": "com/google/android/material/search/SearchBarAnimationHelper.class", "size": 17986, "crc": 1138974148}, {"key": "com/google/android/material/search/SearchView$1.class", "name": "com/google/android/material/search/SearchView$1.class", "size": 1385, "crc": 1700056054}, {"key": "com/google/android/material/search/SearchView$Behavior.class", "name": "com/google/android/material/search/SearchView$Behavior.class", "size": 1880, "crc": 481328693}, {"key": "com/google/android/material/search/SearchView$SavedState$1.class", "name": "com/google/android/material/search/SearchView$SavedState$1.class", "size": 1824, "crc": 827526590}, {"key": "com/google/android/material/search/SearchView$SavedState.class", "name": "com/google/android/material/search/SearchView$SavedState.class", "size": 1809, "crc": -1791811911}, {"key": "com/google/android/material/search/SearchView$TransitionListener.class", "name": "com/google/android/material/search/SearchView$TransitionListener.class", "size": 651, "crc": 417560156}, {"key": "com/google/android/material/search/SearchView$TransitionState.class", "name": "com/google/android/material/search/SearchView$TransitionState.class", "size": 1392, "crc": -578470801}, {"key": "com/google/android/material/search/SearchView.class", "name": "com/google/android/material/search/SearchView.class", "size": 32399, "crc": -1503865019}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$1.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$1.class", "size": 2151, "crc": 2114972385}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$2.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$2.class", "size": 1976, "crc": -1993022864}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$3.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$3.class", "size": 1959, "crc": 79856420}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$4.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$4.class", "size": 1977, "crc": -1231208453}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$5.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$5.class", "size": 1587, "crc": -1361963435}, {"key": "com/google/android/material/search/SearchViewAnimationHelper.class", "name": "com/google/android/material/search/SearchViewAnimationHelper.class", "size": 23527, "crc": 1055238485}, {"key": "com/google/android/material/shadow/ShadowDrawableWrapper.class", "name": "com/google/android/material/shadow/ShadowDrawableWrapper.class", "size": 9308, "crc": 546965893}, {"key": "com/google/android/material/shadow/ShadowRenderer.class", "name": "com/google/android/material/shadow/ShadowRenderer.class", "size": 5630, "crc": 985444890}, {"key": "com/google/android/material/shadow/ShadowViewDelegate.class", "name": "com/google/android/material/shadow/ShadowViewDelegate.class", "size": 413, "crc": -931856479}, {"key": "com/google/android/material/shape/AbsoluteCornerSize.class", "name": "com/google/android/material/shape/AbsoluteCornerSize.class", "size": 1261, "crc": 233209039}, {"key": "com/google/android/material/shape/AdjustedCornerSize.class", "name": "com/google/android/material/shape/AdjustedCornerSize.class", "size": 1787, "crc": -416984879}, {"key": "com/google/android/material/shape/ClampedCornerSize.class", "name": "com/google/android/material/shape/ClampedCornerSize.class", "size": 2074, "crc": -1626132248}, {"key": "com/google/android/material/shape/CornerFamily.class", "name": "com/google/android/material/shape/CornerFamily.class", "size": 396, "crc": -256848325}, {"key": "com/google/android/material/shape/CornerSize.class", "name": "com/google/android/material/shape/CornerSize.class", "size": 273, "crc": 1199154679}, {"key": "com/google/android/material/shape/CornerTreatment.class", "name": "com/google/android/material/shape/CornerTreatment.class", "size": 1441, "crc": 726817671}, {"key": "com/google/android/material/shape/CutCornerTreatment.class", "name": "com/google/android/material/shape/CutCornerTreatment.class", "size": 1216, "crc": -71658657}, {"key": "com/google/android/material/shape/EdgeTreatment.class", "name": "com/google/android/material/shape/EdgeTreatment.class", "size": 1139, "crc": 317612976}, {"key": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper$1.class", "name": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper$1.class", "size": 1018, "crc": -1226478420}, {"key": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper.class", "name": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper.class", "size": 3302, "crc": 601266435}, {"key": "com/google/android/material/shape/MarkerEdgeTreatment.class", "name": "com/google/android/material/shape/MarkerEdgeTreatment.class", "size": 1245, "crc": 1361252397}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$1.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$1.class", "size": 2296, "crc": -7098088}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$2.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$2.class", "size": 1557, "crc": 809916916}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$CompatibilityShadowMode.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$CompatibilityShadowMode.class", "size": 478, "crc": 637944162}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$MaterialShapeDrawableState.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$MaterialShapeDrawableState.class", "size": 3863, "crc": 1331425522}, {"key": "com/google/android/material/shape/MaterialShapeDrawable.class", "name": "com/google/android/material/shape/MaterialShapeDrawable.class", "size": 31106, "crc": 1133174092}, {"key": "com/google/android/material/shape/MaterialShapeUtils.class", "name": "com/google/android/material/shape/MaterialShapeUtils.class", "size": 2315, "crc": 2040468781}, {"key": "com/google/android/material/shape/OffsetEdgeTreatment.class", "name": "com/google/android/material/shape/OffsetEdgeTreatment.class", "size": 1083, "crc": -54374531}, {"key": "com/google/android/material/shape/RelativeCornerSize.class", "name": "com/google/android/material/shape/RelativeCornerSize.class", "size": 2270, "crc": -1797982737}, {"key": "com/google/android/material/shape/RoundedCornerTreatment.class", "name": "com/google/android/material/shape/RoundedCornerTreatment.class", "size": 1125, "crc": 326639898}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$1.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$1.class", "size": 276, "crc": -536041158}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$Builder.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$Builder.class", "size": 9315, "crc": -512010397}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$CornerSizeUnaryOperator.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$CornerSizeUnaryOperator.class", "size": 751, "crc": -297607377}, {"key": "com/google/android/material/shape/ShapeAppearanceModel.class", "name": "com/google/android/material/shape/ShapeAppearanceModel.class", "size": 12146, "crc": -482350160}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$Lazy.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$Lazy.class", "size": 649, "crc": -139910507}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$PathListener.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$PathListener.class", "size": 676, "crc": -721827797}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$ShapeAppearancePathSpec.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$ShapeAppearancePathSpec.class", "size": 1498, "crc": 261780458}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider.class", "size": 10017, "crc": **********}, {"key": "com/google/android/material/shape/ShapePath$1.class", "name": "com/google/android/material/shape/ShapePath$1.class", "size": 1653, "crc": -**********}, {"key": "com/google/android/material/shape/ShapePath$ArcShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$ArcShadowOperation.class", "size": 1903, "crc": -**********}, {"key": "com/google/android/material/shape/ShapePath$InnerCornerShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$InnerCornerShadowOperation.class", "size": 3417, "crc": -783183838}, {"key": "com/google/android/material/shape/ShapePath$LineShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$LineShadowOperation.class", "size": 2306, "crc": -716394277}, {"key": "com/google/android/material/shape/ShapePath$PathArcOperation.class", "name": "com/google/android/material/shape/ShapePath$PathArcOperation.class", "size": 3609, "crc": 99212699}, {"key": "com/google/android/material/shape/ShapePath$PathCubicOperation.class", "name": "com/google/android/material/shape/ShapePath$PathCubicOperation.class", "size": 2469, "crc": 575644246}, {"key": "com/google/android/material/shape/ShapePath$PathLineOperation.class", "name": "com/google/android/material/shape/ShapePath$PathLineOperation.class", "size": 1612, "crc": 524213685}, {"key": "com/google/android/material/shape/ShapePath$PathOperation.class", "name": "com/google/android/material/shape/ShapePath$PathOperation.class", "size": 624, "crc": -706286813}, {"key": "com/google/android/material/shape/ShapePath$PathQuadOperation.class", "name": "com/google/android/material/shape/ShapePath$PathQuadOperation.class", "size": 2512, "crc": 1583140473}, {"key": "com/google/android/material/shape/ShapePath$ShadowCompatOperation.class", "name": "com/google/android/material/shape/ShapePath$ShadowCompatOperation.class", "size": 1121, "crc": 2062136500}, {"key": "com/google/android/material/shape/ShapePath.class", "name": "com/google/android/material/shape/ShapePath.class", "size": 9097, "crc": 1238051900}, {"key": "com/google/android/material/shape/ShapePathModel.class", "name": "com/google/android/material/shape/ShapePathModel.class", "size": 2831, "crc": 1085017495}, {"key": "com/google/android/material/shape/Shapeable.class", "name": "com/google/android/material/shape/Shapeable.class", "size": 450, "crc": 414382740}, {"key": "com/google/android/material/shape/ShapeableDelegate.class", "name": "com/google/android/material/shape/ShapeableDelegate.class", "size": 3964, "crc": -751622067}, {"key": "com/google/android/material/shape/ShapeableDelegateV14.class", "name": "com/google/android/material/shape/ShapeableDelegateV14.class", "size": 1028, "crc": 462920268}, {"key": "com/google/android/material/shape/ShapeableDelegateV22$1.class", "name": "com/google/android/material/shape/ShapeableDelegateV22$1.class", "size": 1462, "crc": -723320103}, {"key": "com/google/android/material/shape/ShapeableDelegateV22.class", "name": "com/google/android/material/shape/ShapeableDelegateV22.class", "size": 4246, "crc": 2035993586}, {"key": "com/google/android/material/shape/ShapeableDelegateV33$1.class", "name": "com/google/android/material/shape/ShapeableDelegateV33$1.class", "size": 1157, "crc": -663029009}, {"key": "com/google/android/material/shape/ShapeableDelegateV33.class", "name": "com/google/android/material/shape/ShapeableDelegateV33.class", "size": 1510, "crc": -2078890336}, {"key": "com/google/android/material/shape/TriangleEdgeTreatment.class", "name": "com/google/android/material/shape/TriangleEdgeTreatment.class", "size": 1082, "crc": 1760295939}, {"key": "com/google/android/material/sidesheet/LeftSheetDelegate.class", "name": "com/google/android/material/sidesheet/LeftSheetDelegate.class", "size": 4289, "crc": 1083039874}, {"key": "com/google/android/material/sidesheet/RightSheetDelegate.class", "name": "com/google/android/material/sidesheet/RightSheetDelegate.class", "size": 4287, "crc": 2024603095}, {"key": "com/google/android/material/sidesheet/Sheet$SheetEdge.class", "name": "com/google/android/material/sidesheet/Sheet$SheetEdge.class", "size": 648, "crc": -287596726}, {"key": "com/google/android/material/sidesheet/Sheet$SheetState.class", "name": "com/google/android/material/sidesheet/Sheet$SheetState.class", "size": 650, "crc": -70621331}, {"key": "com/google/android/material/sidesheet/Sheet$StableSheetState.class", "name": "com/google/android/material/sidesheet/Sheet$StableSheetState.class", "size": 662, "crc": -994783876}, {"key": "com/google/android/material/sidesheet/Sheet.class", "name": "com/google/android/material/sidesheet/Sheet.class", "size": 1036, "crc": -728274646}, {"key": "com/google/android/material/sidesheet/SheetCallback.class", "name": "com/google/android/material/sidesheet/SheetCallback.class", "size": 341, "crc": -619332295}, {"key": "com/google/android/material/sidesheet/SheetDelegate.class", "name": "com/google/android/material/sidesheet/SheetDelegate.class", "size": 1534, "crc": -979399071}, {"key": "com/google/android/material/sidesheet/SheetDialog$1.class", "name": "com/google/android/material/sidesheet/SheetDialog$1.class", "size": 1830, "crc": -1929096659}, {"key": "com/google/android/material/sidesheet/SheetDialog.class", "name": "com/google/android/material/sidesheet/SheetDialog.class", "size": 11275, "crc": -570904860}, {"key": "com/google/android/material/sidesheet/SheetUtils.class", "name": "com/google/android/material/sidesheet/SheetUtils.class", "size": 816, "crc": -1102287812}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$1.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$1.class", "size": 4304, "crc": 1361398490}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$2.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$2.class", "size": 1317, "crc": -1774456330}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState$1.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState$1.class", "size": 2135, "crc": -319522426}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState.class", "size": 2201, "crc": 919926497}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$StateSettlingTracker.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$StateSettlingTracker.class", "size": 2636, "crc": 142260644}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior.class", "size": 36357, "crc": 2046440127}, {"key": "com/google/android/material/sidesheet/SideSheetCallback.class", "name": "com/google/android/material/sidesheet/SideSheetCallback.class", "size": 753, "crc": 1015093689}, {"key": "com/google/android/material/sidesheet/SideSheetDialog$1.class", "name": "com/google/android/material/sidesheet/SideSheetDialog$1.class", "size": 1265, "crc": 1169525691}, {"key": "com/google/android/material/sidesheet/SideSheetDialog.class", "name": "com/google/android/material/sidesheet/SideSheetDialog.class", "size": 5100, "crc": 2017770103}, {"key": "com/google/android/material/slider/BaseOnChangeListener.class", "name": "com/google/android/material/slider/BaseOnChangeListener.class", "size": 645, "crc": 2037947570}, {"key": "com/google/android/material/slider/BaseOnSliderTouchListener.class", "name": "com/google/android/material/slider/BaseOnSliderTouchListener.class", "size": 705, "crc": -466614675}, {"key": "com/google/android/material/slider/BaseSlider$1.class", "name": "com/google/android/material/slider/BaseSlider$1.class", "size": 1748, "crc": 317357350}, {"key": "com/google/android/material/slider/BaseSlider$2.class", "name": "com/google/android/material/slider/BaseSlider$2.class", "size": 1730, "crc": 556627276}, {"key": "com/google/android/material/slider/BaseSlider$AccessibilityEventSender.class", "name": "com/google/android/material/slider/BaseSlider$AccessibilityEventSender.class", "size": 1715, "crc": 2117444443}, {"key": "com/google/android/material/slider/BaseSlider$AccessibilityHelper.class", "name": "com/google/android/material/slider/BaseSlider$AccessibilityHelper.class", "size": 6282, "crc": 1039295314}, {"key": "com/google/android/material/slider/BaseSlider$SliderState$1.class", "name": "com/google/android/material/slider/BaseSlider$SliderState$1.class", "size": 1662, "crc": 424315248}, {"key": "com/google/android/material/slider/BaseSlider$SliderState.class", "name": "com/google/android/material/slider/BaseSlider$SliderState.class", "size": 2479, "crc": -2023810177}, {"key": "com/google/android/material/slider/BaseSlider.class", "name": "com/google/android/material/slider/BaseSlider.class", "size": 58770, "crc": -1957904326}, {"key": "com/google/android/material/slider/BasicLabelFormatter.class", "name": "com/google/android/material/slider/BasicLabelFormatter.class", "size": 1274, "crc": -139680868}, {"key": "com/google/android/material/slider/LabelFormatter.class", "name": "com/google/android/material/slider/LabelFormatter.class", "size": 442, "crc": -964197722}, {"key": "com/google/android/material/slider/RangeSlider$1.class", "name": "com/google/android/material/slider/RangeSlider$1.class", "size": 251, "crc": 97749788}, {"key": "com/google/android/material/slider/RangeSlider$OnChangeListener.class", "name": "com/google/android/material/slider/RangeSlider$OnChangeListener.class", "size": 879, "crc": -2097010055}, {"key": "com/google/android/material/slider/RangeSlider$OnSliderTouchListener.class", "name": "com/google/android/material/slider/RangeSlider$OnSliderTouchListener.class", "size": 1030, "crc": -1090775849}, {"key": "com/google/android/material/slider/RangeSlider$RangeSliderState$1.class", "name": "com/google/android/material/slider/RangeSlider$RangeSliderState$1.class", "size": 1528, "crc": 1908840754}, {"key": "com/google/android/material/slider/RangeSlider$RangeSliderState.class", "name": "com/google/android/material/slider/RangeSlider$RangeSliderState.class", "size": 2570, "crc": 1361972848}, {"key": "com/google/android/material/slider/RangeSlider.class", "name": "com/google/android/material/slider/RangeSlider.class", "size": 13396, "crc": 1769847974}, {"key": "com/google/android/material/slider/Slider$OnChangeListener.class", "name": "com/google/android/material/slider/Slider$OnChangeListener.class", "size": 849, "crc": 623037611}, {"key": "com/google/android/material/slider/Slider$OnSliderTouchListener.class", "name": "com/google/android/material/slider/Slider$OnSliderTouchListener.class", "size": 1000, "crc": 1183341783}, {"key": "com/google/android/material/slider/Slider.class", "name": "com/google/android/material/slider/Slider.class", "size": 10367, "crc": -815228428}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$1.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$1.class", "size": 1053, "crc": 466232939}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$10.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$10.class", "size": 1030, "crc": 1741807796}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$11.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$11.class", "size": 1554, "crc": 1568302125}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$12.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$12.class", "size": 1620, "crc": 1060980634}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$13.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$13.class", "size": 1494, "crc": 1599633238}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$14.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$14.class", "size": 1867, "crc": -47427757}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$15.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$15.class", "size": 1498, "crc": 1714296584}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$16.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$16.class", "size": 1823, "crc": -1487310438}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$2.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$2.class", "size": 2535, "crc": 901272342}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$3.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$3.class", "size": 1707, "crc": 1565588565}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$4.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$4.class", "size": 1764, "crc": 1268398551}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$5.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$5.class", "size": 1348, "crc": -545857754}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$6.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$6.class", "size": 989, "crc": -85621473}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$7.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$7.class", "size": 2315, "crc": 194709732}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$8.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$8.class", "size": 1363, "crc": -798421831}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$9.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$9.class", "size": 975, "crc": -1621077807}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Anchor.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Anchor.class", "size": 3338, "crc": -464725568}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$AnimationMode.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$AnimationMode.class", "size": 705, "crc": -479467311}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback$DismissEvent.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback$DismissEvent.class", "size": 817, "crc": 1989309254}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback.class", "size": 1413, "crc": -1395940737}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Behavior.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Behavior.class", "size": 2422, "crc": 1087663820}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BehaviorDelegate.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BehaviorDelegate.class", "size": 3267, "crc": 258089754}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$ContentViewCallback.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$ContentViewCallback.class", "size": 448, "crc": 517701109}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Duration.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Duration.class", "size": 753, "crc": -1073711778}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout$1.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout$1.class", "size": 1059, "crc": 183277293}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout.class", "size": 10654, "crc": 571142262}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar.class", "size": 29087, "crc": -1055490194}, {"key": "com/google/android/material/snackbar/ContentViewCallback.class", "name": "com/google/android/material/snackbar/ContentViewCallback.class", "size": 219, "crc": 378136704}, {"key": "com/google/android/material/snackbar/Snackbar$Callback.class", "name": "com/google/android/material/snackbar/Snackbar$Callback.class", "size": 1528, "crc": -1133862343}, {"key": "com/google/android/material/snackbar/Snackbar$SnackbarLayout.class", "name": "com/google/android/material/snackbar/Snackbar$SnackbarLayout.class", "size": 3111, "crc": 2123447030}, {"key": "com/google/android/material/snackbar/Snackbar.class", "name": "com/google/android/material/snackbar/Snackbar.class", "size": 11895, "crc": 539196003}, {"key": "com/google/android/material/snackbar/SnackbarContentLayout.class", "name": "com/google/android/material/snackbar/SnackbarContentLayout.class", "size": 5936, "crc": -166863677}, {"key": "com/google/android/material/snackbar/SnackbarManager$1.class", "name": "com/google/android/material/snackbar/SnackbarManager$1.class", "size": 1288, "crc": -1815602522}, {"key": "com/google/android/material/snackbar/SnackbarManager$Callback.class", "name": "com/google/android/material/snackbar/SnackbarManager$Callback.class", "size": 303, "crc": 1755803905}, {"key": "com/google/android/material/snackbar/SnackbarManager$SnackbarRecord.class", "name": "com/google/android/material/snackbar/SnackbarManager$SnackbarRecord.class", "size": 1444, "crc": -570576577}, {"key": "com/google/android/material/snackbar/SnackbarManager.class", "name": "com/google/android/material/snackbar/SnackbarManager.class", "size": 5515, "crc": -309556328}, {"key": "com/google/android/material/stateful/ExtendableSavedState$1.class", "name": "com/google/android/material/stateful/ExtendableSavedState$1.class", "size": 2056, "crc": -1672232444}, {"key": "com/google/android/material/stateful/ExtendableSavedState.class", "name": "com/google/android/material/stateful/ExtendableSavedState.class", "size": 3435, "crc": 1425424509}, {"key": "com/google/android/material/switchmaterial/SwitchMaterial.class", "name": "com/google/android/material/switchmaterial/SwitchMaterial.class", "size": 4896, "crc": 1616685243}, {"key": "com/google/android/material/tabs/ElasticTabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/ElasticTabIndicatorInterpolator.class", "size": 2314, "crc": 1135950159}, {"key": "com/google/android/material/tabs/FadeTabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/FadeTabIndicatorInterpolator.class", "size": 1777, "crc": -675293804}, {"key": "com/google/android/material/tabs/TabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/TabIndicatorInterpolator.class", "size": 3569, "crc": 2140664351}, {"key": "com/google/android/material/tabs/TabItem.class", "name": "com/google/android/material/tabs/TabItem.class", "size": 1470, "crc": 1956373504}, {"key": "com/google/android/material/tabs/TabLayout$1.class", "name": "com/google/android/material/tabs/TabLayout$1.class", "size": 1138, "crc": -452315231}, {"key": "com/google/android/material/tabs/TabLayout$AdapterChangeListener.class", "name": "com/google/android/material/tabs/TabLayout$AdapterChangeListener.class", "size": 1492, "crc": 553076542}, {"key": "com/google/android/material/tabs/TabLayout$BaseOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$BaseOnTabSelectedListener.class", "size": 671, "crc": 1050407423}, {"key": "com/google/android/material/tabs/TabLayout$LabelVisibility.class", "name": "com/google/android/material/tabs/TabLayout$LabelVisibility.class", "size": 284, "crc": 1731175528}, {"key": "com/google/android/material/tabs/TabLayout$Mode.class", "name": "com/google/android/material/tabs/TabLayout$Mode.class", "size": 640, "crc": 1079253831}, {"key": "com/google/android/material/tabs/TabLayout$OnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$OnTabSelectedListener.class", "size": 596, "crc": -23902816}, {"key": "com/google/android/material/tabs/TabLayout$PagerAdapterObserver.class", "name": "com/google/android/material/tabs/TabLayout$PagerAdapterObserver.class", "size": 808, "crc": 2129456040}, {"key": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator$1.class", "name": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator$1.class", "size": 1526, "crc": 1642920512}, {"key": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator.class", "name": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator.class", "size": 8483, "crc": 1353328767}, {"key": "com/google/android/material/tabs/TabLayout$Tab.class", "name": "com/google/android/material/tabs/TabLayout$Tab.class", "size": 7560, "crc": -1068844595}, {"key": "com/google/android/material/tabs/TabLayout$TabGravity.class", "name": "com/google/android/material/tabs/TabLayout$TabGravity.class", "size": 652, "crc": -1198687105}, {"key": "com/google/android/material/tabs/TabLayout$TabIndicatorAnimationMode.class", "name": "com/google/android/material/tabs/TabLayout$TabIndicatorAnimationMode.class", "size": 682, "crc": -1589472116}, {"key": "com/google/android/material/tabs/TabLayout$TabIndicatorGravity.class", "name": "com/google/android/material/tabs/TabLayout$TabIndicatorGravity.class", "size": 670, "crc": -309239603}, {"key": "com/google/android/material/tabs/TabLayout$TabLayoutOnPageChangeListener.class", "name": "com/google/android/material/tabs/TabLayout$TabLayoutOnPageChangeListener.class", "size": 2410, "crc": 1802785229}, {"key": "com/google/android/material/tabs/TabLayout$TabView$1.class", "name": "com/google/android/material/tabs/TabLayout$TabView$1.class", "size": 1298, "crc": -352417930}, {"key": "com/google/android/material/tabs/TabLayout$TabView.class", "name": "com/google/android/material/tabs/TabLayout$TabView.class", "size": 21202, "crc": -313059543}, {"key": "com/google/android/material/tabs/TabLayout$ViewPagerOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$ViewPagerOnTabSelectedListener.class", "size": 1366, "crc": -347978570}, {"key": "com/google/android/material/tabs/TabLayout.class", "name": "com/google/android/material/tabs/TabLayout.class", "size": 43058, "crc": -1526459569}, {"key": "com/google/android/material/tabs/TabLayoutMediator$PagerAdapterObserver.class", "name": "com/google/android/material/tabs/TabLayoutMediator$PagerAdapterObserver.class", "size": 1648, "crc": 2100237708}, {"key": "com/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy.class", "name": "com/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy.class", "size": 567, "crc": 723086534}, {"key": "com/google/android/material/tabs/TabLayoutMediator$TabLayoutOnPageChangeCallback.class", "name": "com/google/android/material/tabs/TabLayoutMediator$TabLayoutOnPageChangeCallback.class", "size": 2488, "crc": 530719037}, {"key": "com/google/android/material/tabs/TabLayoutMediator$ViewPagerOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayoutMediator$ViewPagerOnTabSelectedListener.class", "size": 1514, "crc": 920280675}, {"key": "com/google/android/material/tabs/TabLayoutMediator.class", "name": "com/google/android/material/tabs/TabLayoutMediator.class", "size": 5915, "crc": 533264397}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate$1.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate$1.class", "size": 1087, "crc": 1831853852}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate$2.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate$2.class", "size": 1085, "crc": 523815408}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate.class", "size": 8778, "crc": 1148614585}, {"key": "com/google/android/material/textfield/CustomEndIconDelegate.class", "name": "com/google/android/material/textfield/CustomEndIconDelegate.class", "size": 989, "crc": 558531213}, {"key": "com/google/android/material/textfield/CutoutDrawable$1.class", "name": "com/google/android/material/textfield/CutoutDrawable$1.class", "size": 266, "crc": 618456396}, {"key": "com/google/android/material/textfield/CutoutDrawable$CutoutDrawableState.class", "name": "com/google/android/material/textfield/CutoutDrawable$CutoutDrawableState.class", "size": 2663, "crc": -2043920266}, {"key": "com/google/android/material/textfield/CutoutDrawable$ImplApi14.class", "name": "com/google/android/material/textfield/CutoutDrawable$ImplApi14.class", "size": 3873, "crc": -1933903413}, {"key": "com/google/android/material/textfield/CutoutDrawable$ImplApi18.class", "name": "com/google/android/material/textfield/CutoutDrawable$ImplApi18.class", "size": 2106, "crc": -135848548}, {"key": "com/google/android/material/textfield/CutoutDrawable.class", "name": "com/google/android/material/textfield/CutoutDrawable.class", "size": 3791, "crc": -1195687090}, {"key": "com/google/android/material/textfield/DropdownMenuEndIconDelegate$1.class", "name": "com/google/android/material/textfield/DropdownMenuEndIconDelegate$1.class", "size": 1145, "crc": 1508722563}, {"key": "com/google/android/material/textfield/DropdownMenuEndIconDelegate.class", "name": "com/google/android/material/textfield/DropdownMenuEndIconDelegate.class", "size": 14024, "crc": 335411524}, {"key": "com/google/android/material/textfield/EditTextUtils.class", "name": "com/google/android/material/textfield/EditTextUtils.class", "size": 663, "crc": -1066444781}, {"key": "com/google/android/material/textfield/EndCompoundLayout$1.class", "name": "com/google/android/material/textfield/EndCompoundLayout$1.class", "size": 1281, "crc": -1198704974}, {"key": "com/google/android/material/textfield/EndCompoundLayout$2.class", "name": "com/google/android/material/textfield/EndCompoundLayout$2.class", "size": 2543, "crc": -478259290}, {"key": "com/google/android/material/textfield/EndCompoundLayout$3.class", "name": "com/google/android/material/textfield/EndCompoundLayout$3.class", "size": 1174, "crc": -101849124}, {"key": "com/google/android/material/textfield/EndCompoundLayout$EndIconDelegates.class", "name": "com/google/android/material/textfield/EndCompoundLayout$EndIconDelegates.class", "size": 3081, "crc": 1961468268}, {"key": "com/google/android/material/textfield/EndCompoundLayout.class", "name": "com/google/android/material/textfield/EndCompoundLayout.class", "size": 28693, "crc": -1132015193}, {"key": "com/google/android/material/textfield/EndIconDelegate.class", "name": "com/google/android/material/textfield/EndIconDelegate.class", "size": 4348, "crc": -1571436656}, {"key": "com/google/android/material/textfield/IconHelper.class", "name": "com/google/android/material/textfield/IconHelper.class", "size": 6656, "crc": 868772580}, {"key": "com/google/android/material/textfield/IndicatorViewController$1.class", "name": "com/google/android/material/textfield/IndicatorViewController$1.class", "size": 2002, "crc": 154861149}, {"key": "com/google/android/material/textfield/IndicatorViewController$2.class", "name": "com/google/android/material/textfield/IndicatorViewController$2.class", "size": 1550, "crc": 1471531176}, {"key": "com/google/android/material/textfield/IndicatorViewController.class", "name": "com/google/android/material/textfield/IndicatorViewController.class", "size": 17946, "crc": -129785022}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView$1.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView$1.class", "size": 2444, "crc": -747061937}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView$MaterialArrayAdapter.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView$MaterialArrayAdapter.class", "size": 5152, "crc": -1564240748}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView.class", "size": 13735, "crc": 1453029382}, {"key": "com/google/android/material/textfield/NoEndIconDelegate.class", "name": "com/google/android/material/textfield/NoEndIconDelegate.class", "size": 617, "crc": -725570219}, {"key": "com/google/android/material/textfield/PasswordToggleEndIconDelegate.class", "name": "com/google/android/material/textfield/PasswordToggleEndIconDelegate.class", "size": 4276, "crc": 811518504}, {"key": "com/google/android/material/textfield/StartCompoundLayout.class", "name": "com/google/android/material/textfield/StartCompoundLayout.class", "size": 13405, "crc": 2128684993}, {"key": "com/google/android/material/textfield/TextInputEditText.class", "name": "com/google/android/material/textfield/TextInputEditText.class", "size": 6914, "crc": -1418759058}, {"key": "com/google/android/material/textfield/TextInputLayout$1.class", "name": "com/google/android/material/textfield/TextInputLayout$1.class", "size": 1726, "crc": -675079911}, {"key": "com/google/android/material/textfield/TextInputLayout$2.class", "name": "com/google/android/material/textfield/TextInputLayout$2.class", "size": 1017, "crc": -1415134485}, {"key": "com/google/android/material/textfield/TextInputLayout$3.class", "name": "com/google/android/material/textfield/TextInputLayout$3.class", "size": 861, "crc": -663458880}, {"key": "com/google/android/material/textfield/TextInputLayout$4.class", "name": "com/google/android/material/textfield/TextInputLayout$4.class", "size": 1376, "crc": -1963054402}, {"key": "com/google/android/material/textfield/TextInputLayout$AccessibilityDelegate.class", "name": "com/google/android/material/textfield/TextInputLayout$AccessibilityDelegate.class", "size": 4839, "crc": 271848347}, {"key": "com/google/android/material/textfield/TextInputLayout$BoxBackgroundMode.class", "name": "com/google/android/material/textfield/TextInputLayout$BoxBackgroundMode.class", "size": 456, "crc": 1092778348}, {"key": "com/google/android/material/textfield/TextInputLayout$EndIconMode.class", "name": "com/google/android/material/textfield/TextInputLayout$EndIconMode.class", "size": 682, "crc": -294596871}, {"key": "com/google/android/material/textfield/TextInputLayout$LengthCounter.class", "name": "com/google/android/material/textfield/TextInputLayout$LengthCounter.class", "size": 405, "crc": 1538680510}, {"key": "com/google/android/material/textfield/TextInputLayout$OnEditTextAttachedListener.class", "name": "com/google/android/material/textfield/TextInputLayout$OnEditTextAttachedListener.class", "size": 469, "crc": 1956016059}, {"key": "com/google/android/material/textfield/TextInputLayout$OnEndIconChangedListener.class", "name": "com/google/android/material/textfield/TextInputLayout$OnEndIconChangedListener.class", "size": 466, "crc": -2057357360}, {"key": "com/google/android/material/textfield/TextInputLayout$SavedState$1.class", "name": "com/google/android/material/textfield/TextInputLayout$SavedState$1.class", "size": 2117, "crc": -2089963979}, {"key": "com/google/android/material/textfield/TextInputLayout$SavedState.class", "name": "com/google/android/material/textfield/TextInputLayout$SavedState.class", "size": 2642, "crc": 1658115504}, {"key": "com/google/android/material/textfield/TextInputLayout.class", "name": "com/google/android/material/textfield/TextInputLayout.class", "size": 81813, "crc": -1850122947}, {"key": "com/google/android/material/textview/MaterialTextView.class", "name": "com/google/android/material/textview/MaterialTextView.class", "size": 4948, "crc": -862327479}, {"key": "com/google/android/material/theme/MaterialComponentsViewInflater.class", "name": "com/google/android/material/theme/MaterialComponentsViewInflater.class", "size": 2168, "crc": 1207662008}, {"key": "com/google/android/material/theme/overlay/MaterialThemeOverlay.class", "name": "com/google/android/material/theme/overlay/MaterialThemeOverlay.class", "size": 2864, "crc": 1591733474}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView$1.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView$1.class", "size": 292, "crc": 610428292}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView$TextFormatter.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView$TextFormatter.class", "size": 2015, "crc": -1429044964}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView.class", "size": 7620, "crc": 1917019625}, {"key": "com/google/android/material/timepicker/ClickActionDelegate.class", "name": "com/google/android/material/timepicker/ClickActionDelegate.class", "size": 1427, "crc": 474035729}, {"key": "com/google/android/material/timepicker/ClockFaceView$1.class", "name": "com/google/android/material/timepicker/ClockFaceView$1.class", "size": 1561, "crc": -1507822255}, {"key": "com/google/android/material/timepicker/ClockFaceView$2.class", "name": "com/google/android/material/timepicker/ClockFaceView$2.class", "size": 3528, "crc": 409790047}, {"key": "com/google/android/material/timepicker/ClockFaceView.class", "name": "com/google/android/material/timepicker/ClockFaceView.class", "size": 13421, "crc": -990350907}, {"key": "com/google/android/material/timepicker/ClockHandView$1.class", "name": "com/google/android/material/timepicker/ClockHandView$1.class", "size": 898, "crc": 1157602827}, {"key": "com/google/android/material/timepicker/ClockHandView$OnActionUpListener.class", "name": "com/google/android/material/timepicker/ClockHandView$OnActionUpListener.class", "size": 433, "crc": -26417129}, {"key": "com/google/android/material/timepicker/ClockHandView$OnRotateListener.class", "name": "com/google/android/material/timepicker/ClockHandView$OnRotateListener.class", "size": 427, "crc": 1378255424}, {"key": "com/google/android/material/timepicker/ClockHandView.class", "name": "com/google/android/material/timepicker/ClockHandView.class", "size": 13207, "crc": 955056804}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$1.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$1.class", "size": 1430, "crc": -1005524794}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$2.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$2.class", "size": 1431, "crc": 634784360}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$3.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$3.class", "size": 1534, "crc": 254301847}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$Builder.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$Builder.class", "size": 4484, "crc": 201385342}, {"key": "com/google/android/material/timepicker/MaterialTimePicker.class", "name": "com/google/android/material/timepicker/MaterialTimePicker.class", "size": 20258, "crc": 207550712}, {"key": "com/google/android/material/timepicker/MaxInputValidator.class", "name": "com/google/android/material/timepicker/MaxInputValidator.class", "size": 1532, "crc": 1885974659}, {"key": "com/google/android/material/timepicker/RadialViewGroup.class", "name": "com/google/android/material/timepicker/RadialViewGroup.class", "size": 7816, "crc": 1416985267}, {"key": "com/google/android/material/timepicker/TimeFormat.class", "name": "com/google/android/material/timepicker/TimeFormat.class", "size": 405, "crc": 884932036}, {"key": "com/google/android/material/timepicker/TimeModel$1.class", "name": "com/google/android/material/timepicker/TimeModel$1.class", "size": 1244, "crc": -215588388}, {"key": "com/google/android/material/timepicker/TimeModel.class", "name": "com/google/android/material/timepicker/TimeModel.class", "size": 5267, "crc": 351146431}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter$1.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter$1.class", "size": 1946, "crc": 663617567}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter$2.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter$2.class", "size": 2000, "crc": 425116309}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter.class", "size": 8376, "crc": -221071098}, {"key": "com/google/android/material/timepicker/TimePickerControls$ActiveSelection.class", "name": "com/google/android/material/timepicker/TimePickerControls$ActiveSelection.class", "size": 463, "crc": 474981328}, {"key": "com/google/android/material/timepicker/TimePickerControls$ClockPeriod.class", "name": "com/google/android/material/timepicker/TimePickerControls$ClockPeriod.class", "size": 455, "crc": 825056715}, {"key": "com/google/android/material/timepicker/TimePickerControls.class", "name": "com/google/android/material/timepicker/TimePickerControls.class", "size": 685, "crc": 905062974}, {"key": "com/google/android/material/timepicker/TimePickerPresenter.class", "name": "com/google/android/material/timepicker/TimePickerPresenter.class", "size": 236, "crc": 1380867493}, {"key": "com/google/android/material/timepicker/TimePickerTextInputKeyController.class", "name": "com/google/android/material/timepicker/TimePickerTextInputKeyController.class", "size": 4057, "crc": 981655598}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$1.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$1.class", "size": 1514, "crc": 274275563}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$2.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$2.class", "size": 1510, "crc": -934806185}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$3.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$3.class", "size": 1292, "crc": -1492397141}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$4.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$4.class", "size": 2021, "crc": -1031288660}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$5.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$5.class", "size": 2078, "crc": -566194364}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter.class", "size": 11123, "crc": 89324959}, {"key": "com/google/android/material/timepicker/TimePickerView$1.class", "name": "com/google/android/material/timepicker/TimePickerView$1.class", "size": 1423, "crc": -1326692029}, {"key": "com/google/android/material/timepicker/TimePickerView$2.class", "name": "com/google/android/material/timepicker/TimePickerView$2.class", "size": 1343, "crc": 1209888299}, {"key": "com/google/android/material/timepicker/TimePickerView$3.class", "name": "com/google/android/material/timepicker/TimePickerView$3.class", "size": 1253, "crc": 1170271940}, {"key": "com/google/android/material/timepicker/TimePickerView$OnDoubleTapListener.class", "name": "com/google/android/material/timepicker/TimePickerView$OnDoubleTapListener.class", "size": 308, "crc": 1989955054}, {"key": "com/google/android/material/timepicker/TimePickerView$OnPeriodChangeListener.class", "name": "com/google/android/material/timepicker/TimePickerView$OnPeriodChangeListener.class", "size": 318, "crc": -447963850}, {"key": "com/google/android/material/timepicker/TimePickerView$OnSelectionChange.class", "name": "com/google/android/material/timepicker/TimePickerView$OnSelectionChange.class", "size": 312, "crc": 45893329}, {"key": "com/google/android/material/timepicker/TimePickerView.class", "name": "com/google/android/material/timepicker/TimePickerView.class", "size": 11467, "crc": 1695609307}, {"key": "com/google/android/material/tooltip/TooltipDrawable$1.class", "name": "com/google/android/material/tooltip/TooltipDrawable$1.class", "size": 1135, "crc": -2800762}, {"key": "com/google/android/material/tooltip/TooltipDrawable.class", "name": "com/google/android/material/tooltip/TooltipDrawable.class", "size": 14143, "crc": 840678468}, {"key": "com/google/android/material/transformation/ExpandableBehavior$1.class", "name": "com/google/android/material/transformation/ExpandableBehavior$1.class", "size": 1770, "crc": 580162253}, {"key": "com/google/android/material/transformation/ExpandableBehavior.class", "name": "com/google/android/material/transformation/ExpandableBehavior.class", "size": 5476, "crc": 2145863530}, {"key": "com/google/android/material/transformation/ExpandableTransformationBehavior$1.class", "name": "com/google/android/material/transformation/ExpandableTransformationBehavior$1.class", "size": 1197, "crc": -83548322}, {"key": "com/google/android/material/transformation/ExpandableTransformationBehavior.class", "name": "com/google/android/material/transformation/ExpandableTransformationBehavior.class", "size": 2263, "crc": -372434806}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$1.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$1.class", "size": 1497, "crc": 1742746762}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$2.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$2.class", "size": 1482, "crc": 1636869156}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$3.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$3.class", "size": 1793, "crc": -1139916405}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$4.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$4.class", "size": 1967, "crc": -2140778451}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$FabTransformationSpec.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$FabTransformationSpec.class", "size": 781, "crc": 1215565229}, {"key": "com/google/android/material/transformation/FabTransformationBehavior.class", "name": "com/google/android/material/transformation/FabTransformationBehavior.class", "size": 23595, "crc": -1788183633}, {"key": "com/google/android/material/transformation/FabTransformationScrimBehavior$1.class", "name": "com/google/android/material/transformation/FabTransformationScrimBehavior$1.class", "size": 1391, "crc": -1812583336}, {"key": "com/google/android/material/transformation/FabTransformationScrimBehavior.class", "name": "com/google/android/material/transformation/FabTransformationScrimBehavior.class", "size": 4300, "crc": 864857150}, {"key": "com/google/android/material/transformation/FabTransformationSheetBehavior.class", "name": "com/google/android/material/transformation/FabTransformationSheetBehavior.class", "size": 4747, "crc": -103562305}, {"key": "com/google/android/material/transformation/TransformationChildCard.class", "name": "com/google/android/material/transformation/TransformationChildCard.class", "size": 789, "crc": -2002201880}, {"key": "com/google/android/material/transformation/TransformationChildLayout.class", "name": "com/google/android/material/transformation/TransformationChildLayout.class", "size": 925, "crc": -1056582726}, {"key": "com/google/android/material/transition/FadeModeEvaluator.class", "name": "com/google/android/material/transition/FadeModeEvaluator.class", "size": 237, "crc": 1640018920}, {"key": "com/google/android/material/transition/FadeModeEvaluators$1.class", "name": "com/google/android/material/transition/FadeModeEvaluators$1.class", "size": 1094, "crc": 1091996556}, {"key": "com/google/android/material/transition/FadeModeEvaluators$2.class", "name": "com/google/android/material/transition/FadeModeEvaluators$2.class", "size": 1096, "crc": 1866514278}, {"key": "com/google/android/material/transition/FadeModeEvaluators$3.class", "name": "com/google/android/material/transition/FadeModeEvaluators$3.class", "size": 1103, "crc": -1718752992}, {"key": "com/google/android/material/transition/FadeModeEvaluators$4.class", "name": "com/google/android/material/transition/FadeModeEvaluators$4.class", "size": 1190, "crc": -**********}, {"key": "com/google/android/material/transition/FadeModeEvaluators.class", "name": "com/google/android/material/transition/FadeModeEvaluators.class", "size": 1695, "crc": **********}, {"key": "com/google/android/material/transition/FadeModeResult.class", "name": "com/google/android/material/transition/FadeModeResult.class", "size": 751, "crc": -**********}, {"key": "com/google/android/material/transition/FadeProvider$1.class", "name": "com/google/android/material/transition/FadeProvider$1.class", "size": 1409, "crc": -**********}, {"key": "com/google/android/material/transition/FadeProvider$2.class", "name": "com/google/android/material/transition/FadeProvider$2.class", "size": 923, "crc": -**********}, {"key": "com/google/android/material/transition/FadeProvider.class", "name": "com/google/android/material/transition/FadeProvider.class", "size": 2556, "crc": 262211550}, {"key": "com/google/android/material/transition/FadeThroughProvider$1.class", "name": "com/google/android/material/transition/FadeThroughProvider$1.class", "size": 1444, "crc": -**********}, {"key": "com/google/android/material/transition/FadeThroughProvider$2.class", "name": "com/google/android/material/transition/FadeThroughProvider$2.class", "size": 958, "crc": -**********}, {"key": "com/google/android/material/transition/FadeThroughProvider.class", "name": "com/google/android/material/transition/FadeThroughProvider.class", "size": 2678, "crc": 381103974}, {"key": "com/google/android/material/transition/FitModeEvaluator.class", "name": "com/google/android/material/transition/FitModeEvaluator.class", "size": 434, "crc": 35785930}, {"key": "com/google/android/material/transition/FitModeEvaluators$1.class", "name": "com/google/android/material/transition/FitModeEvaluators$1.class", "size": 1921, "crc": **********}, {"key": "com/google/android/material/transition/FitModeEvaluators$2.class", "name": "com/google/android/material/transition/FitModeEvaluators$2.class", "size": 1956, "crc": 215905094}, {"key": "com/google/android/material/transition/FitModeEvaluators.class", "name": "com/google/android/material/transition/FitModeEvaluators.class", "size": 2077, "crc": -**********}, {"key": "com/google/android/material/transition/FitModeResult.class", "name": "com/google/android/material/transition/FitModeResult.class", "size": 689, "crc": -**********}, {"key": "com/google/android/material/transition/Hold.class", "name": "com/google/android/material/transition/Hold.class", "size": 1172, "crc": -1023259333}, {"key": "com/google/android/material/transition/MaskEvaluator.class", "name": "com/google/android/material/transition/MaskEvaluator.class", "size": 3575, "crc": -637407766}, {"key": "com/google/android/material/transition/MaterialArcMotion.class", "name": "com/google/android/material/transition/MaterialArcMotion.class", "size": 1137, "crc": -83012138}, {"key": "com/google/android/material/transition/MaterialContainerTransform$1.class", "name": "com/google/android/material/transition/MaterialContainerTransform$1.class", "size": 1672, "crc": 211645409}, {"key": "com/google/android/material/transition/MaterialContainerTransform$2.class", "name": "com/google/android/material/transition/MaterialContainerTransform$2.class", "size": 2613, "crc": 447499216}, {"key": "com/google/android/material/transition/MaterialContainerTransform$FadeMode.class", "name": "com/google/android/material/transition/MaterialContainerTransform$FadeMode.class", "size": 711, "crc": 1930053133}, {"key": "com/google/android/material/transition/MaterialContainerTransform$FitMode.class", "name": "com/google/android/material/transition/MaterialContainerTransform$FitMode.class", "size": 709, "crc": -1165114754}, {"key": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholds.class", "name": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholds.class", "size": 1302, "crc": 1959600104}, {"key": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholdsGroup.class", "name": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholdsGroup.class", "size": 2688, "crc": 1283120198}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDirection.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDirection.class", "size": 733, "crc": -1465281821}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$1.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$1.class", "size": 1395, "crc": -1319203475}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$2.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$2.class", "size": 1393, "crc": 51092075}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable.class", "size": 17741, "crc": -1886955081}, {"key": "com/google/android/material/transition/MaterialContainerTransform.class", "name": "com/google/android/material/transition/MaterialContainerTransform.class", "size": 22110, "crc": 1202651229}, {"key": "com/google/android/material/transition/MaterialElevationScale.class", "name": "com/google/android/material/transition/MaterialElevationScale.class", "size": 2832, "crc": -542282495}, {"key": "com/google/android/material/transition/MaterialFade.class", "name": "com/google/android/material/transition/MaterialFade.class", "size": 4153, "crc": -2032793447}, {"key": "com/google/android/material/transition/MaterialFadeThrough.class", "name": "com/google/android/material/transition/MaterialFadeThrough.class", "size": 3354, "crc": 1876473596}, {"key": "com/google/android/material/transition/MaterialSharedAxis$Axis.class", "name": "com/google/android/material/transition/MaterialSharedAxis$Axis.class", "size": 679, "crc": **********}, {"key": "com/google/android/material/transition/MaterialSharedAxis.class", "name": "com/google/android/material/transition/MaterialSharedAxis.class", "size": 4174, "crc": 196287596}, {"key": "com/google/android/material/transition/MaterialVisibility.class", "name": "com/google/android/material/transition/MaterialVisibility.class", "size": 6153, "crc": -474678158}, {"key": "com/google/android/material/transition/ScaleProvider$1.class", "name": "com/google/android/material/transition/ScaleProvider$1.class", "size": 1009, "crc": -**********}, {"key": "com/google/android/material/transition/ScaleProvider.class", "name": "com/google/android/material/transition/ScaleProvider.class", "size": 3720, "crc": -185796541}, {"key": "com/google/android/material/transition/SlideDistanceProvider$1.class", "name": "com/google/android/material/transition/SlideDistanceProvider$1.class", "size": 978, "crc": **********}, {"key": "com/google/android/material/transition/SlideDistanceProvider$2.class", "name": "com/google/android/material/transition/SlideDistanceProvider$2.class", "size": 978, "crc": -798020424}, {"key": "com/google/android/material/transition/SlideDistanceProvider$GravityFlag.class", "name": "com/google/android/material/transition/SlideDistanceProvider$GravityFlag.class", "size": 702, "crc": -133214578}, {"key": "com/google/android/material/transition/SlideDistanceProvider.class", "name": "com/google/android/material/transition/SlideDistanceProvider.class", "size": 5528, "crc": -**********}, {"key": "com/google/android/material/transition/TransitionListenerAdapter.class", "name": "com/google/android/material/transition/TransitionListenerAdapter.class", "size": 1044, "crc": -569856687}, {"key": "com/google/android/material/transition/TransitionUtils$1.class", "name": "com/google/android/material/transition/TransitionUtils$1.class", "size": 1905, "crc": 503608410}, {"key": "com/google/android/material/transition/TransitionUtils$CornerSizeBinaryOperator.class", "name": "com/google/android/material/transition/TransitionUtils$CornerSizeBinaryOperator.class", "size": 584, "crc": -**********}, {"key": "com/google/android/material/transition/TransitionUtils.class", "name": "com/google/android/material/transition/TransitionUtils.class", "size": 14181, "crc": -413602292}, {"key": "com/google/android/material/transition/VisibilityAnimatorProvider.class", "name": "com/google/android/material/transition/VisibilityAnimatorProvider.class", "size": 493, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluator.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluator.class", "size": 351, "crc": 775996075}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$1.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$1.class", "size": 1166, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$2.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$2.class", "size": 1168, "crc": -361421897}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$3.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$3.class", "size": 1175, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$4.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$4.class", "size": 1262, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators.class", "size": 1881, "crc": -555559969}, {"key": "com/google/android/material/transition/platform/FadeModeResult.class", "name": "com/google/android/material/transition/platform/FadeModeResult.class", "size": 874, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeProvider$1.class", "name": "com/google/android/material/transition/platform/FadeProvider$1.class", "size": 1445, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeProvider$2.class", "name": "com/google/android/material/transition/platform/FadeProvider$2.class", "size": 950, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeProvider.class", "name": "com/google/android/material/transition/platform/FadeProvider.class", "size": 2667, "crc": 985812888}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider$1.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider$1.class", "size": 1480, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider$2.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider$2.class", "size": 985, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider.class", "size": 2789, "crc": 743918317}, {"key": "com/google/android/material/transition/platform/FitModeEvaluator.class", "name": "com/google/android/material/transition/platform/FitModeEvaluator.class", "size": 566, "crc": -804062383}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators$1.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators$1.class", "size": 2011, "crc": **********}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators$2.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators$2.class", "size": 2046, "crc": **********}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators.class", "size": 2245, "crc": 305996515}, {"key": "com/google/android/material/transition/platform/FitModeResult.class", "name": "com/google/android/material/transition/platform/FitModeResult.class", "size": 803, "crc": 1634221981}, {"key": "com/google/android/material/transition/platform/Hold.class", "name": "com/google/android/material/transition/platform/Hold.class", "size": 1252, "crc": -1097154407}, {"key": "com/google/android/material/transition/platform/MaskEvaluator.class", "name": "com/google/android/material/transition/platform/MaskEvaluator.class", "size": 3734, "crc": -683291781}, {"key": "com/google/android/material/transition/platform/MaterialArcMotion.class", "name": "com/google/android/material/transition/platform/MaterialArcMotion.class", "size": 1220, "crc": 899328616}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$1.class", "size": 1751, "crc": -726119402}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$2.class", "size": 2695, "crc": 1272585624}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$FadeMode.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$FadeMode.class", "size": 729, "crc": 501787041}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$FitMode.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$FitMode.class", "size": 727, "crc": -92533106}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholds.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholds.class", "size": 1338, "crc": -1279122291}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholdsGroup.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholdsGroup.class", "size": 2850, "crc": 492518810}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDirection.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDirection.class", "size": 751, "crc": -2119850022}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$1.class", "size": 1458, "crc": 193253541}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$2.class", "size": 1456, "crc": -1311504784}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable.class", "size": 18097, "crc": -237715912}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform.class", "size": 22527, "crc": -1860810028}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$1.class", "size": 1387, "crc": 212983209}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$2.class", "size": 1763, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$3.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$3.class", "size": 1318, "crc": -**********}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeProvider.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeProvider.class", "size": 647, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeableViewShapeProvider.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeableViewShapeProvider.class", "size": 1444, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback.class", "size": 10096, "crc": -173061584}, {"key": "com/google/android/material/transition/platform/MaterialElevationScale.class", "name": "com/google/android/material/transition/platform/MaterialElevationScale.class", "size": 3022, "crc": 20403054}, {"key": "com/google/android/material/transition/platform/MaterialFade.class", "name": "com/google/android/material/transition/platform/MaterialFade.class", "size": 4352, "crc": 317631913}, {"key": "com/google/android/material/transition/platform/MaterialFadeThrough.class", "name": "com/google/android/material/transition/platform/MaterialFadeThrough.class", "size": 3544, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialSharedAxis$Axis.class", "name": "com/google/android/material/transition/platform/MaterialSharedAxis$Axis.class", "size": 697, "crc": -838282202}, {"key": "com/google/android/material/transition/platform/MaterialSharedAxis.class", "name": "com/google/android/material/transition/platform/MaterialSharedAxis.class", "size": 4373, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialVisibility.class", "name": "com/google/android/material/transition/platform/MaterialVisibility.class", "size": 6356, "crc": -**********}, {"key": "com/google/android/material/transition/platform/ScaleProvider$1.class", "name": "com/google/android/material/transition/platform/ScaleProvider$1.class", "size": 1036, "crc": -**********}, {"key": "com/google/android/material/transition/platform/ScaleProvider.class", "name": "com/google/android/material/transition/platform/ScaleProvider.class", "size": 3822, "crc": -**********}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$1.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$1.class", "size": 1005, "crc": **********}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$2.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$2.class", "size": 1005, "crc": -813052143}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$GravityFlag.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$GravityFlag.class", "size": 720, "crc": 5394307}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider.class", "size": 5648, "crc": -384834124}, {"key": "com/google/android/material/transition/platform/TransitionListenerAdapter.class", "name": "com/google/android/material/transition/platform/TransitionListenerAdapter.class", "size": 1154, "crc": 48592457}, {"key": "com/google/android/material/transition/platform/TransitionUtils$1.class", "name": "com/google/android/material/transition/platform/TransitionUtils$1.class", "size": 1941, "crc": -13018350}, {"key": "com/google/android/material/transition/platform/TransitionUtils$CornerSizeBinaryOperator.class", "name": "com/google/android/material/transition/platform/TransitionUtils$CornerSizeBinaryOperator.class", "size": 602, "crc": -**********}, {"key": "com/google/android/material/transition/platform/TransitionUtils.class", "name": "com/google/android/material/transition/platform/TransitionUtils.class", "size": 14286, "crc": 357289160}, {"key": "com/google/android/material/transition/platform/VisibilityAnimatorProvider.class", "name": "com/google/android/material/transition/platform/VisibilityAnimatorProvider.class", "size": 568, "crc": -**********}, {"key": "META-INF/com.google.android.material_material.version", "name": "META-INF/com.google.android.material_material.version", "size": 7, "crc": -806041522}]