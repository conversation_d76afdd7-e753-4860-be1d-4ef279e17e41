<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽手柄 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:alpha="0.4" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_equalizer"
            android:tint="@color/primary"
            android:contentDescription="EQ设置图标" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="准备导入EQ设置"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="@color/text_secondary"
            android:contentDescription="关闭"
            android:padding="8dp" />

    </LinearLayout>

    <!-- 操作指引卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/primary_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 操作指引标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="操作指引"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/on_primary_container" />

            <!-- 操作步骤列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 步骤1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:text="1"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/on_primary"
                        android:background="@drawable/step_number_background"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="打开&quot;欢律&quot;耳机App"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

                <!-- 步骤2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:text="2"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/on_primary"
                        android:background="@drawable/step_number_background"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="进入&quot;大师调音&quot;→&quot;EQ设置&quot;界面"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

                <!-- 步骤3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginTop="2dp"
                        android:text="3"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/on_primary"
                        android:background="@drawable/step_number_background"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="添加一个命名为&quot;Seeq&quot;的自定义调音"
                        android:textSize="14sp"
                        android:textColor="@color/on_primary_container" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 重要提示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@drawable/warning_background">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/ic_info"
            android:tint="@color/primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="只有添加了名为&quot;Seeq&quot;的自定义调音，才能成功导入EQ设置"
            android:textSize="14sp"
            android:textColor="@color/primary"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 我知道了按钮 -->
        <Button
            android:id="@+id/btn_understand"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="我知道了"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button"
            app:icon="@drawable/ic_check"
            app:iconGravity="start" />

        <!-- 不再提示按钮 -->
        <Button
            android:id="@+id/btn_dont_show_again"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="不再提示"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            app:icon="@drawable/ic_visibility_off"
            app:iconGravity="start" />

    </LinearLayout>

</LinearLayout>
