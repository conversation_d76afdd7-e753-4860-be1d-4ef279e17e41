package cn.ykload.seeq

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 用户数据管理类
 */
object UserManager {
    
    private const val TAG = "UserManager"
    private const val PREFS_NAME = "seeq_user_prefs"
    private const val KEY_AUTH_TOKEN = "auth_token"
    private const val KEY_DONT_SHOW_EQ_GUIDE = "dont_show_eq_guide"
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 保存authToken
     */
    fun saveUserID(context: Context, authToken: String) {
        try {
            getPreferences(context).edit()
                .putString(KEY_AUTH_TOKEN, authToken)
                .apply()
            Log.d(TAG, "authToken已保存: $authToken")
        } catch (e: Exception) {
            Log.e(TAG, "保存authToken失败", e)
        }
    }
    
    /**
     * 获取authToken
     */
    fun getUserID(context: Context): String? {
        return try {
            val authToken = getPreferences(context).getString(KEY_AUTH_TOKEN, null)
            Log.d(TAG, "获取authToken: ${authToken ?: "未找到"}")
            authToken
        } catch (e: Exception) {
            Log.e(TAG, "获取authToken失败", e)
            null
        }
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(context: Context): Boolean {
        val authToken = getUserID(context)
        val isLoggedIn = !authToken.isNullOrEmpty() && authToken.length == 16
        Log.d(TAG, "登录状态检查: $isLoggedIn")
        return isLoggedIn
    }
    
    /**
     * 清除用户数据（登出）
     */
    fun clearUserData(context: Context) {
        try {
            getPreferences(context).edit()
                .remove(KEY_AUTH_TOKEN)
                .apply()
            Log.d(TAG, "用户数据已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除用户数据失败", e)
        }
    }

    /**
     * 保存"不再显示EQ指引"设置
     */
    fun setDontShowEQGuide(context: Context, dontShow: Boolean) {
        try {
            getPreferences(context).edit()
                .putBoolean(KEY_DONT_SHOW_EQ_GUIDE, dontShow)
                .apply()
            Log.d(TAG, "EQ指引显示设置已保存: $dontShow")
        } catch (e: Exception) {
            Log.e(TAG, "保存EQ指引显示设置失败", e)
        }
    }

    /**
     * 获取"不再显示EQ指引"设置
     */
    fun shouldShowEQGuide(context: Context): Boolean {
        return try {
            val dontShow = getPreferences(context).getBoolean(KEY_DONT_SHOW_EQ_GUIDE, false)
            val shouldShow = !dontShow
            Log.d(TAG, "EQ指引显示设置: shouldShow=$shouldShow, dontShow=$dontShow")
            shouldShow
        } catch (e: Exception) {
            Log.e(TAG, "获取EQ指引显示设置失败", e)
            true // 默认显示
        }
    }
    
    /**
     * 验证authToken格式
     */
    fun isValidUserID(authToken: String?): Boolean {
        if (authToken.isNullOrEmpty()) return false
        
        // 检查是否为16位数字
        val isValid = authToken.length == 16 && authToken.all { it.isDigit() }
        Log.d(TAG, "authToken格式验证: $authToken -> $isValid")
        return isValid
    }
}
