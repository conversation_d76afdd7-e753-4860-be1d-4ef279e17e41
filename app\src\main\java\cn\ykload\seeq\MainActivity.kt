package cn.ykload.seeq

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowInsetsController
import android.webkit.*
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.textfield.TextInputEditText
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern

class MainActivity : AppCompatActivity(), AutomationCallback {

    companion object {
        private const val TAG = "MainActivity"
        private const val SEEQ_URL = "https://seeq.ykload.com"
        private val EQ_PATTERN = Pattern.compile("\\[Seeq\\]\\{([^}]+)\\}")
        private val COOLAPK_PATTERN = Pattern.compile("\\[Seeq\\]\\{toCoolapk:([^}]+)\\}")

        // 权限请求码
        private const val PERMISSION_REQUEST_CODE = 1001

        // 登录相关常量
        private const val QQ_GROUP_COMMAND = "小真寻，登录码，欧内盖~"
        private const val QQ_GROUP_URL = "mqqapi://card/show_pslcard?src_type=internal&version=1&uin=976836736&card_type=group&source=qrcode"

        // 应用前台状态标志
        @Volatile
        private var isAppInForeground = false

        /**
         * 检查应用是否在前台
         */
        fun isInForeground(): Boolean = isAppInForeground
    }

    // 剪贴板监听器
    private var clipboardListener: ClipboardManager.OnPrimaryClipChangedListener? = null

    private lateinit var webView: WebView

    // 文件上传相关
    private var fileUploadCallback: ValueCallback<Array<Uri>>? = null
    private var cameraPhotoPath: String? = null

    // Activity Result Launchers
    private lateinit var fileChooserLauncher: ActivityResultLauncher<Intent>
    private lateinit var permissionLauncher: ActivityResultLauncher<Array<String>>

    // 启动图层相关
    private lateinit var splashOverlay: LinearLayout
    private lateinit var logoContainer: LinearLayout
    private lateinit var loadingContainer: LinearLayout
    private lateinit var loadingText: TextView
    private var isPageLoadedSuccessfully = false
    private var hasShownSplashAnimation = false

    // 防止重复弹出模态框的标志位
    private var isPermissionDialogShowing = false

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 配置系统UI（在setContentView之前调用）
        setupSystemUI()

        // 设置布局
        setContentView(R.layout.activity_main)

        // 初始化Activity Result Launchers
        initializeActivityResultLaunchers()

        // 获取WebView引用
        webView = findViewById(R.id.webView)

        // 初始化启动图层控件
        initializeSplashOverlay()

        // 配置WebView
        setupWebView()

        // 设置自动化完成回调
        SeqAccessibilityService.instance?.setAutomationCallback(this)

        // 初始化通知渠道
        NotificationHelper.createNotificationChannel(this)

        // 检查通知权限
        checkNotificationPermission()

        // 检查网络连接
        checkNetworkConnection()
    }



    /**
     * 配置系统UI
     */
    private fun setupSystemUI() {
        try {
            // 确保内容不延伸到系统栏
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(true)
            }

            // 设置纯白色状态栏，深色图标
            window.statusBarColor = Color.WHITE

            // 确保状态栏图标为深色（在白色背景上可见）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.insetsController?.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                )
                // 同时设置导航栏图标为深色
                window.insetsController?.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS
                )
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                @Suppress("DEPRECATION")
                var flags = View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    flags = flags or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
                }
                window.decorView.systemUiVisibility = flags
            }

            // 导航栏保持透明
            window.navigationBarColor = Color.TRANSPARENT

            Log.d(TAG, "系统UI配置完成 - 纯白色状态栏，深色图标")
            Log.d(TAG, "状态栏颜色: ${String.format("#%08X", window.statusBarColor)}")
            Log.d(TAG, "导航栏颜色: ${String.format("#%08X", window.navigationBarColor)}")
        } catch (e: Exception) {
            Log.e(TAG, "设置系统UI时出错: ${e.message}")
            // 降级处理：设置基本的状态栏颜色
            window.statusBarColor = Color.WHITE
        }
    }

    /**
     * 初始化启动图层
     */
    private fun initializeSplashOverlay() {
        // 获取启动图层控件引用
        splashOverlay = findViewById(R.id.splash_overlay)
        logoContainer = findViewById(R.id.logo_container)
        loadingContainer = findViewById(R.id.loading_container)
        loadingText = findViewById(R.id.loading_text)

        Log.d(TAG, "启动图层控件初始化完成")
        Log.d(TAG, "splashOverlay: $splashOverlay")
        Log.d(TAG, "logoContainer: $logoContainer")
        Log.d(TAG, "loadingContainer: $loadingContainer")
        Log.d(TAG, "loadingText: $loadingText")

        // 启动logo入场动画
        startSplashAnimation()
    }

    /**
     * 启动启动画面动画
     */
    private fun startSplashAnimation() {
        if (hasShownSplashAnimation) return
        hasShownSplashAnimation = true

        Log.d(TAG, "开始启动画面动画")

        // 确保启动图层可见
        splashOverlay.visibility = View.VISIBLE

        // 先让元素立即可见，然后执行动画
        logoContainer.visibility = View.VISIBLE
        loadingContainer.visibility = View.VISIBLE

        // 使用post确保在下一个UI循环中执行动画
        logoContainer.post {
            // Logo容器动画：淡入 + 缩放 + 向上移动
            logoContainer.alpha = 0f
            logoContainer.scaleX = 0.8f
            logoContainer.scaleY = 0.8f
            logoContainer.translationY = 30f

            Log.d(TAG, "Logo容器初始状态设置完成")

            logoContainer.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .translationY(0f)
                .setDuration(800)
                .setInterpolator(android.view.animation.DecelerateInterpolator())
                .withStartAction {
                    Log.d(TAG, "Logo动画开始")
                }
                .withEndAction {
                    Log.d(TAG, "Logo动画结束")
                }
                .start()
        }

        loadingContainer.post {
            // 加载指示器延迟淡入动画
            loadingContainer.alpha = 0f
            loadingContainer.translationY = 20f

            Log.d(TAG, "加载容器初始状态设置完成")

            loadingContainer.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(600)
                .setStartDelay(400)
                .setInterpolator(android.view.animation.DecelerateInterpolator())
                .withStartAction {
                    Log.d(TAG, "加载动画开始")
                }
                .withEndAction {
                    Log.d(TAG, "加载动画结束")
                }
                .start()
        }

        // 更新加载文本
        updateLoadingText("正在连接服务器...")
    }

    /**
     * 更新加载文本
     */
    private fun updateLoadingText(text: String) {
        runOnUiThread {
            loadingText.text = text
        }
    }

    /**
     * 隐藏启动图层
     */
    private fun hideSplashOverlay() {
        if (!isPageLoadedSuccessfully) return

        Log.d(TAG, "开始隐藏启动图层")

        // 延迟0.5秒后开始复杂的退出动画
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            startExitAnimation()
        }, 500) // 0.5秒延迟
    }

    /**
     * 启动退出动画序列
     */
    private fun startExitAnimation() {
        val appLogo = findViewById<android.widget.ImageView>(R.id.app_logo)

        // 等待logo图片加载完成后获取实际尺寸
        appLogo.post {
            // 获取logo的实际尺寸
            val logoWidth = appLogo.width
            val logoHeight = appLogo.height

            // 简化计算：让logo向上移动到屏幕顶部
            // 使用logoContainer的当前Y位置加上logo高度，这样logo会移动到屏幕顶部边缘
            val currentY = logoContainer.y
            val moveDistance = -(currentY + logoHeight/2 - 50)

            Log.d(TAG, "Logo实际尺寸: ${logoWidth}x${logoHeight}")
            Log.d(TAG, "Logo容器当前Y位置: $currentY")
            Log.d(TAG, "计算的移动距离: $moveDistance")

            // 第一阶段：logo向上移动 + 缩放到40% + 提前淡出
            logoContainer.animate()
                .translationY(moveDistance)
                .scaleX(0.4f)  // 缩放到40%
                .scaleY(0.4f)  // 缩放到40%
                .setDuration(800)
                .setInterpolator(android.view.animation.DecelerateInterpolator(1.5f)) // ease-out动画曲线
                .withStartAction {
                    Log.d(TAG, "开始logo向上移动 + 缩放到40%动画")
                    Log.d(TAG, "动画前logo容器Y位置: ${logoContainer.y}")
                    Log.d(TAG, "将要移动的距离: $moveDistance")
                    // 隐藏其他元素
                    hideOtherElements()
                    // 启动提前淡出动画
                    startEarlyFadeOut()
                }
                .withEndAction {
                    Log.d(TAG, "Logo移动和缩放完成")
                    Log.d(TAG, "动画后logo容器Y位置: ${logoContainer.y}")
                    Log.d(TAG, "动画后logo容器translationY: ${logoContainer.translationY}")
                    // 确保完全隐藏
                    splashOverlay.visibility = View.GONE
                }
                .start()
        }
    }

    /**
     * 启动提前淡出动画（在移动动画的75%时开始）
     */
    private fun startEarlyFadeOut() {
        // 延迟600ms后开始淡出（800ms * 0.75 = 600ms）
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            Log.d(TAG, "开始提前淡出动画")

            splashOverlay.animate()
                .alpha(0f)
                .setDuration(200) // 200ms快速淡出
                .setInterpolator(android.view.animation.AccelerateInterpolator())
                .withEndAction {
                    Log.d(TAG, "提前淡出完成")
                }
                .start()
        }, 600) // 在移动动画的75%时开始淡出
    }

    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Float {
        var result = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = resources.getDimensionPixelSize(resourceId)
        }
        return result.toFloat()
    }



    /**
     * 隐藏其他元素
     */
    private fun hideOtherElements() {
        Log.d(TAG, "隐藏其他UI元素")

        // 淡出加载容器和底部信息
        loadingContainer.animate()
            .alpha(0f)
            .setDuration(300)
            .start()

        // 淡出副标题
        logoContainer.getChildAt(1)?.animate()
            ?.alpha(0f)
            ?.setDuration(300)
            ?.start()

        // 淡出底部版本信息
        splashOverlay.getChildAt(2)?.animate()
            ?.alpha(0f)
            ?.setDuration(300)
            ?.start()
    }



    /**
     * 初始化Activity Result Launchers
     */
    private fun initializeActivityResultLaunchers() {
        // 文件选择器启动器
        fileChooserLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleFileChooserResult(result.resultCode, result.data)
        }

        // 权限请求启动器
        permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            handlePermissionResult(permissions)
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!NotificationHelper.areNotificationsEnabled(this)) {
                Log.d(TAG, "通知权限未授予，将在需要时请求")
                // 不在这里强制请求权限，而是在需要时再请求
                // 这样可以避免在应用启动时就弹出权限请求
            } else {
                Log.d(TAG, "通知权限已授予")
            }
        } else {
            Log.d(TAG, "Android 12及以下版本，无需请求通知权限")
        }
    }

    /**
     * 检查网络连接
     */
    private fun checkNetworkConnection() {
        Log.d(TAG, "开始检查网络连接...")

        // 首先快速检查设备网络状态
        if (!NetworkHelper.hasNetworkConnection(this)) {
            Log.w(TAG, "设备无网络连接")
            showNetworkErrorDialog()
            return
        }

        // 设备有网络连接，进一步检查服务器可达性
        NetworkHelper.checkSeeqConnection(object : NetworkHelper.NetworkCheckCallback {
            override fun onNetworkAvailable() {
                Log.d(TAG, "网络连接正常，开始启动流程")
                updateLoadingText("网络连接正常")
                checkLoginStatus()
            }

            override fun onNetworkUnavailable() {
                Log.w(TAG, "无法连接到Seeq服务器")
                updateLoadingText("无法连接到服务器")
                showNetworkErrorDialog()
            }
        })
    }

    /**
     * 检查登录状态
     */
    private fun checkLoginStatus() {
        if (UserManager.isLoggedIn(this)) {
            val authToken = UserManager.getUserID(this)
            Log.d(TAG, "用户已登录，authToken: $authToken")
            loadWebViewWithUserID(authToken!!)
        } else {
            Log.d(TAG, "用户未登录，显示登录弹窗")
            showLoginDialog()
        }
    }

    /**
     * 使用authToken加载WebView
     */
    private fun loadWebViewWithUserID(authToken: String) {
        // 设置包含authToken的User-Agent
        val originalUserAgent = webView.settings.userAgentString
        val customUserAgent = "$originalUserAgent SeeqApp/1.0 authToken/$authToken"
        webView.settings.userAgentString = customUserAgent

        Log.d(TAG, "设置User-Agent: $customUserAgent")
        updateLoadingText("少女祈祷中...")

        // 加载网页
        webView.loadUrl(SEEQ_URL)
        Log.d(TAG, "WebView已开始加载: $SEEQ_URL")
    }

    /**
     * 配置WebView设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val webSettings = webView.settings

        // 启用JavaScript
        webSettings.javaScriptEnabled = true

        // 启用DOM存储
        webSettings.domStorageEnabled = true

        // 启用数据库存储
        webSettings.databaseEnabled = true


        // 设置缓存模式
        webSettings.cacheMode = WebSettings.LOAD_DEFAULT

        // 允许文件访问
        webSettings.allowFileAccess = true

        // 允许内容访问
        webSettings.allowContentAccess = true

        // 完全禁用所有缩放功能
        webSettings.setSupportZoom(false)
        webSettings.builtInZoomControls = false
        webSettings.displayZoomControls = false

        // 禁用多点触控缩放
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            webSettings.setBuiltInZoomControls(false)
        }

        // 设置初始缩放比例为100%并禁用用户缩放
        webSettings.loadWithOverviewMode = false
        webSettings.useWideViewPort = false

        // 设置默认缩放比例
        webSettings.defaultZoom = WebSettings.ZoomDensity.MEDIUM

        // 设置用户代理
        webSettings.userAgentString = webSettings.userAgentString + " SeeqApp/1.0"

        // 设置WebViewClient
        webView.webViewClient = SeeqWebViewClient()

        // 设置WebChromeClient用于监听控制台消息
        webView.webChromeClient = SeeqWebChromeClient()

        // 添加JavaScript接口（在WebView初始化时就注册）
        webView.addJavascriptInterface(JavaScriptInterface(), "Android")

        // 初始化剪贴板监听
        setupClipboardListener()

        // 禁用WebView的触摸缩放手势
        webView.setOnTouchListener { _, event ->
            // 检测多点触控手势并阻止
            if (event.pointerCount > 1) {
                return@setOnTouchListener true // 消费事件，阻止缩放
            }
            false // 允许单点触控事件继续传递
        }

        // 隐藏滚动条但保留滚动功能
        webView.isVerticalScrollBarEnabled = false
        webView.isHorizontalScrollBarEnabled = false

        // 设置滚动条样式为不显示（额外保险）
        webView.scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY

        Log.d(TAG, "WebView配置完成")
    }

    /**
     * 自定义WebViewClient
     */
    private inner class SeeqWebViewClient : WebViewClient() {

        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            // 允许在当前WebView中加载所有URL
            return false
        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
            super.onPageStarted(view, url, favicon)
            Log.d(TAG, "开始加载页面: $url")
            updateLoadingText("少女祈祷中...")
            isPageLoadedSuccessfully = false
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            Log.d(TAG, "页面加载完成: $url")

            // 检查页面是否成功加载（通过检查页面内容）
            checkPageLoadSuccess(view)

            // 注入JavaScript代码来监听控制台输出
            injectConsoleMonitor()
        }

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
            super.onReceivedError(view, request, error)
            Log.e(TAG, "WebView加载错误: ${error?.description}")

            // 如果是主页面加载错误，显示网络错误对话框
            if (request?.isForMainFrame == true) {
                updateLoadingText("页面加载失败")
                showNetworkErrorDialog()
            }
        }

        override fun onReceivedHttpError(view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?) {
            super.onReceivedHttpError(view, request, errorResponse)

            // 检查HTTP状态码
            val statusCode = errorResponse?.statusCode ?: 0
            Log.e(TAG, "HTTP错误: $statusCode - ${errorResponse?.reasonPhrase}")

            // 如果是主页面的HTTP错误（如404、500等），显示网络错误对话框
            if (request?.isForMainFrame == true && statusCode >= 400) {
                updateLoadingText("服务器错误 ($statusCode)")
                showNetworkErrorDialog()
            }
        }
    }

    // 页面加载检测相关变量
    private var pageLoadCheckHandler = Handler(Looper.getMainLooper())
    private var pageLoadCheckRunnable: Runnable? = null
    private var pageLoadCheckAttempts = 0
    private val MAX_PAGE_LOAD_CHECK_ATTEMPTS = 10 // 最多检查10次
    private val PAGE_LOAD_CHECK_INTERVAL = 1500L // 每1.5秒检查一次

    /**
     * 检查页面加载成功 - 改进版本
     * 使用延迟检查机制，等待页面真正可用
     */
    private fun checkPageLoadSuccess(webView: WebView?) {
        Log.d(TAG, "开始检查页面加载状态...")
        pageLoadCheckAttempts = 0
        startPageLoadCheck(webView)
    }

    /**
     * 开始页面加载检查
     */
    private fun startPageLoadCheck(webView: WebView?) {
        // 取消之前的检查任务
        cancelPageLoadCheck()

        pageLoadCheckRunnable = Runnable {
            performPageLoadCheck(webView)
        }

        // 延迟执行检查，给页面更多时间加载
        pageLoadCheckHandler.postDelayed(pageLoadCheckRunnable!!, PAGE_LOAD_CHECK_INTERVAL)
    }

    /**
     * 执行页面加载检查
     */
    private fun performPageLoadCheck(webView: WebView?) {
        pageLoadCheckAttempts++
        Log.d(TAG, "执行页面加载检查，第 $pageLoadCheckAttempts 次")

        webView?.evaluateJavascript(
            """
            (function() {
                try {
                    // 基础检查
                    var hasBody = document.body !== null;
                    var hasContent = document.body && document.body.innerHTML.length > 100;
                    var hasTitle = document.title && document.title.length > 0;

                    // 错误页面检查
                    var isErrorPage = document.body && (
                        document.body.innerHTML.includes('404') ||
                        document.body.innerHTML.includes('Error') ||
                        document.body.innerHTML.includes('错误') ||
                        document.body.innerHTML.includes('Not Found') ||
                        document.body.innerHTML.includes('服务器错误') ||
                        document.body.innerHTML.includes('网络错误')
                    );

                    // 检查页面是否还在加载中
                    var isLoading = document.readyState !== 'complete';

                    // 检查是否有主要内容区域（根据常见的网站结构）
                    var hasMainContent = document.querySelector('main') !== null ||
                                       document.querySelector('#main') !== null ||
                                       document.querySelector('.main') !== null ||
                                       document.querySelector('#content') !== null ||
                                       document.querySelector('.content') !== null ||
                                       document.querySelector('article') !== null ||
                                       document.querySelector('.container') !== null;

                    // 检查是否有可交互元素（按钮、链接、表单等）
                    var hasInteractiveElements = document.querySelectorAll('button, a, input, select, textarea').length > 0;

                    // 检查JavaScript是否正常工作（通过检查是否有事件监听器）
                    var hasJavaScriptActivity = window.jQuery !== undefined ||
                                              window.React !== undefined ||
                                              window.Vue !== undefined ||
                                              document.querySelectorAll('[onclick]').length > 0;

                    return {
                        hasBody: hasBody,
                        hasContent: hasContent,
                        hasTitle: hasTitle,
                        isErrorPage: isErrorPage,
                        isLoading: isLoading,
                        hasMainContent: hasMainContent,
                        hasInteractiveElements: hasInteractiveElements,
                        hasJavaScriptActivity: hasJavaScriptActivity,
                        title: document.title,
                        bodyLength: document.body ? document.body.innerHTML.length : 0,
                        readyState: document.readyState,
                        url: window.location.href
                    };
                } catch (e) {
                    return {
                        error: e.toString(),
                        hasBody: false,
                        hasContent: false,
                        isErrorPage: false
                    };
                }
            })();
            """.trimIndent()
        ) { result ->
            try {
                Log.d(TAG, "页面检查结果 (第${pageLoadCheckAttempts}次): $result")

                if (result == null || result == "null") {
                    handlePageLoadCheckFailure("检查结果为空")
                    return@evaluateJavascript
                }

                // 解析结果
                val isErrorPage = result.contains("\"isErrorPage\":true")
                val hasContent = result.contains("\"hasContent\":true")
                val isLoading = result.contains("\"isLoading\":true")
                val hasMainContent = result.contains("\"hasMainContent\":true")
                val hasInteractiveElements = result.contains("\"hasInteractiveElements\":true")
                val hasError = result.contains("\"error\":")

                if (hasError) {
                    Log.w(TAG, "页面检查时发生JavaScript错误: $result")
                    handlePageLoadCheckFailure("JavaScript执行错误")
                    return@evaluateJavascript
                }

                if (isErrorPage) {
                    Log.w(TAG, "检测到错误页面")
                    handlePageLoadCheckFailure("错误页面")
                    return@evaluateJavascript
                }

                // 判断页面是否真正加载完成并可用
                val isPageReady = hasContent && !isLoading && (hasMainContent || hasInteractiveElements)

                if (isPageReady) {
                    Log.d(TAG, "页面加载检查通过，页面已准备就绪")
                    handlePageLoadCheckSuccess()
                } else if (pageLoadCheckAttempts >= MAX_PAGE_LOAD_CHECK_ATTEMPTS) {
                    Log.w(TAG, "页面加载检查达到最大尝试次数，但页面可能仍在加载中")
                    // 达到最大尝试次数，但如果有基本内容就认为成功
                    if (hasContent && !isErrorPage) {
                        Log.d(TAG, "虽然达到最大尝试次数，但页面有内容，认为加载成功")
                        handlePageLoadCheckSuccess()
                    } else {
                        handlePageLoadCheckFailure("达到最大检查次数但页面仍未就绪")
                    }
                } else {
                    Log.d(TAG, "页面尚未完全就绪，继续检查... (hasContent: $hasContent, isLoading: $isLoading, hasMainContent: $hasMainContent, hasInteractiveElements: $hasInteractiveElements)")
                    updateLoadingText("页面加载中... (${pageLoadCheckAttempts}/${MAX_PAGE_LOAD_CHECK_ATTEMPTS})")
                    // 继续下一次检查
                    startPageLoadCheck(webView)
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理页面检查结果时出错", e)
                handlePageLoadCheckFailure("处理检查结果异常: ${e.message}")
            }
        }
    }

    /**
     * 处理页面加载检查成功
     */
    private fun handlePageLoadCheckSuccess() {
        cancelPageLoadCheck()
        isPageLoadedSuccessfully = true
        updateLoadingText("Beta版本，请多加反馈~")
        hideSplashOverlay()
        Log.d(TAG, "页面加载成功，隐藏启动页面")
    }

    /**
     * 处理页面加载检查失败
     */
    private fun handlePageLoadCheckFailure(reason: String) {
        cancelPageLoadCheck()
        Log.w(TAG, "页面加载检查失败: $reason")
        updateLoadingText("页面加载异常")
        showNetworkErrorDialog()
    }

    /**
     * 取消页面加载检查
     */
    private fun cancelPageLoadCheck() {
        pageLoadCheckRunnable?.let { runnable ->
            pageLoadCheckHandler.removeCallbacks(runnable)
            pageLoadCheckRunnable = null
        }
    }

    /**
     * 自定义WebChromeClient用于监听控制台消息和处理文件上传
     */
    private inner class SeeqWebChromeClient : WebChromeClient() {

        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
            consoleMessage?.let { message ->
                val logMessage = "[WebView Console] ${message.message()}"
                Log.d(TAG, logMessage)

                // 检查是否包含Seeq EQ参数
                checkForEQParameters(message.message())
            }
            return true
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            Log.d(TAG, "页面加载进度: $newProgress%")

            // 更新加载文本显示进度
            if (newProgress < 100) {
                updateLoadingText("页面加载中... $newProgress%")
            } else {
                Log.d(TAG, "页面加载进度达到100%，但可能还有资源在加载")
                updateLoadingText("页面加载中...")
            }
        }

        // 拦截JavaScript alert弹窗
        override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            Log.d(TAG, "拦截到JavaScript Alert: $message")

            // 显示自定义底部模态框
            showAlertBottomSheet(message ?: "", "提示", "alert")

            // 确认结果
            result?.confirm()

            // 返回true表示已处理，不显示默认弹窗
            return true
        }

        // 拦截JavaScript confirm弹窗
        override fun onJsConfirm(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            Log.d(TAG, "拦截到JavaScript Confirm: $message")

            // 显示自定义底部模态框，并传递result用于处理用户选择
            showConfirmBottomSheet(message ?: "", "确认", result)

            // 返回true表示已处理，不显示默认弹窗
            return true
        }

        // 拦截JavaScript prompt弹窗
        override fun onJsPrompt(view: WebView?, url: String?, message: String?, defaultValue: String?, result: JsPromptResult?): Boolean {
            Log.d(TAG, "拦截到JavaScript Prompt: $message")

            // 显示自定义底部模态框，并传递result用于处理用户输入
            showPromptBottomSheet(message ?: "", "输入", defaultValue ?: "", result)

            // 返回true表示已处理，不显示默认弹窗
            return true
        }

        // 处理文件上传（Android 5.0+）
        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            Log.d(TAG, "文件选择器被触发")

            // 保存回调
            fileUploadCallback = filePathCallback

            // 检查权限并启动文件选择
            if (checkAndRequestPermissions()) {
                showFileChooser(fileChooserParams)
            }

            return true
        }
    }

    /**
     * 注入JavaScript代码来监听控制台输出和拦截toast
     */
    private fun injectConsoleMonitor() {
        val jsCode = """
            (function() {
                // 保存原始的console.log方法
                var originalLog = console.log;
                var originalWarn = console.warn;
                var originalError = console.error;
                var originalInfo = console.info;

                // 重写console.log方法
                console.log = function() {
                    originalLog.apply(console, arguments);
                    // 通过Android接口发送消息
                    if (window.Android) {
                        window.Android.onConsoleMessage('log', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.warn = function() {
                    originalWarn.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('warn', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.error = function() {
                    originalError.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('error', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.info = function() {
                    originalInfo.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('info', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                // 拦截toast相关调用
                // 拦截可能的toast库调用
                if (typeof window.toast !== 'undefined') {
                    var originalToast = window.toast;
                    window.toast = function(message, options) {
                        if (window.Android && window.Android.showToast) {
                            var type = (options && options.type) || 'info';
                            window.Android.showToast(String(message), type);
                        } else {
                            return originalToast.apply(this, arguments);
                        }
                    };
                }

                // 拦截常见的toast方法
                ['success', 'error', 'warning', 'info'].forEach(function(type) {
                    if (typeof window[type] !== 'undefined') {
                        var originalMethod = window[type];
                        window[type] = function(message) {
                            if (window.Android && window.Android.showToast) {
                                window.Android.showToast(String(message), type);
                            } else {
                                return originalMethod.apply(this, arguments);
                            }
                        };
                    }
                });

                // 创建全局toast方法
                window.showToast = function(message, type) {
                    type = type || 'info';
                    if (window.Android && window.Android.showToast) {
                        window.Android.showToast(String(message), type);
                    } else {
                        console.log('[Toast] ' + type + ': ' + message);
                    }
                };

                // 创建全局alert方法（用于JavaScript主动调用）
                window.showAlert = function(message, title, type) {
                    title = title || '提示';
                    type = type || 'alert';
                    if (window.Android && window.Android.showAlert) {
                        window.Android.showAlert(String(message), title, type);
                    } else {
                        console.log('[Alert] ' + type + ': ' + title + ' - ' + message);
                    }
                };

                // 设置返回事件处理
                window.setBackPressedHandler = function(handler) {
                    if (typeof handler === 'function') {
                        window.onAndroidBackPressed = handler;
                        console.log('返回事件处理器已设置');
                    }
                };

                // 添加返回事件监听器的便捷方法
                window.addBackPressedListener = function(listener) {
                    if (typeof listener === 'function') {
                        document.addEventListener('androidBackPressed', function(event) {
                            try {
                                var result = listener(event);
                                if (result === true) {
                                    event.preventDefault();
                                }
                            } catch (e) {
                                console.error('Error in back pressed listener:', e);
                            }
                        });
                        console.log('返回事件监听器已添加');
                    }
                };

                // 添加获取剪贴板内容的方法
                window.getClipboardText = function() {
                    if (window.Android && window.Android.getClipboardText) {
                        return window.Android.getClipboardText();
                    } else {
                        console.error('Android接口不可用，无法获取剪贴板内容');
                        return '';
                    }
                };

                // 重写navigator.clipboard.readText方法，使其使用Android接口
                if (navigator.clipboard) {
                    navigator.clipboard.readText = function() {
                        return new Promise(function(resolve, reject) {
                            try {
                                var text = window.getClipboardText();
                                resolve(text);
                            } catch (error) {
                                reject(error);
                            }
                        });
                    };
                }

                // 添加剪贴板更改事件监听
                window.addEventListener('clipboardChanged', function() {
                    console.log('剪贴板内容已更改，可以重新获取');
                });

                // 提供设置剪贴板更改回调的方法
                window.setClipboardChangeCallback = function(callback) {
                    if (typeof callback === 'function') {
                        window.onClipboardChanged = callback;
                        console.log('剪贴板更改回调已设置');
                    }
                };

                // 添加强制刷新剪贴板的方法
                window.refreshClipboard = function() {
                    if (window.Android && window.Android.refreshClipboard) {
                        return window.Android.refreshClipboard();
                    } else {
                        console.warn('Android接口不可用，无法刷新剪贴板');
                        return window.getClipboardText();
                    }
                };

                // 检查Android接口是否可用
                if (window.Android) {
                    console.log('Android接口对象存在');
                    if (window.Android.getClipboardText) {
                        console.log('getClipboardText方法可用');
                    } else {
                        console.error('getClipboardText方法不可用');
                    }
                    if (window.Android.testAndroidInterface) {
                        var testResult = window.Android.testAndroidInterface();
                        console.log('Android接口测试结果: ' + testResult);
                    }
                } else {
                    console.error('Android接口对象不存在');
                }

                console.log('Seeq控制台监听、Toast、Alert拦截、返回事件处理和剪贴板功能已启动');
            })();
        """.trimIndent()

        // 注入JavaScript代码（JavaScript接口已在setupWebView中注册）
        webView.evaluateJavascript(jsCode) { result ->
            Log.d(TAG, "控制台监听和Toast拦截脚本注入完成: $result")
        }
    }

    /**
     * JavaScript接口类
     */
    private inner class JavaScriptInterface {

        @JavascriptInterface
        fun onConsoleMessage(level: String, message: String) {
            Log.d(TAG, "[$level] $message")

            // 在主线程中处理控制台消息
            runOnUiThread {
                checkForEQParameters(message)
            }
        }

        @JavascriptInterface
        fun showToast(message: String, type: String = "info") {
            Log.d(TAG, "WebView Toast: [$type] $message")

            // 在主线程中显示底部模态框
            runOnUiThread {
                showToastBottomSheet(message, type)
            }
        }

        @JavascriptInterface
        fun showAlert(message: String, title: String = "提示", type: String = "alert") {
            Log.d(TAG, "WebView Alert: [$type] $title - $message")

            // 在主线程中显示底部模态框
            runOnUiThread {
                showAlertBottomSheet(message, title, type)
            }
        }

        @JavascriptInterface
        fun getClipboardText(): String {
            return try {
                // 每次都重新获取剪贴板管理器，确保获取最新内容
                val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = clipboard.primaryClip
                if (clip != null && clip.itemCount > 0) {
                    val text = clip.getItemAt(0).text?.toString() ?: ""
                    Log.d(TAG, "获取剪贴板内容: $text (时间戳: ${System.currentTimeMillis()})")
                    text
                } else {
                    Log.d(TAG, "剪贴板为空 (时间戳: ${System.currentTimeMillis()})")
                    ""
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取剪贴板内容失败", e)
                ""
            }
        }

        @JavascriptInterface
        fun testAndroidInterface(): String {
            Log.d(TAG, "Android接口测试方法被调用")
            return "Android接口工作正常"
        }

        @JavascriptInterface
        fun refreshClipboard(): String {
            Log.d(TAG, "强制刷新剪贴板内容")
            // 强制触发剪贴板更改通知
            notifyWebViewClipboardChanged()
            return getClipboardText()
        }

        @JavascriptInterface
        fun onBackPressedHandled(handled: Boolean) {
            Log.d(TAG, "WebView返回事件处理结果: $handled")
            // 这个方法可以用于WebView主动告知Android是否处理了返回事件
            // 目前通过evaluateJavascript的回调已经能获取结果，所以这个方法作为备用
        }
    }

    /**
     * 处理系统返回事件
     */
    @SuppressLint("GestureBackNavigation")
    override fun onBackPressed() {
        // 先尝试将返回事件传递给WebView的JavaScript处理
        handleBackPressedInWebView { handled ->
            if (!handled) {
                // 如果JavaScript没有处理，检查WebView是否可以返回上一页
                if (webView.canGoBack()) {
                    webView.goBack()
                } else {
                    // 如果WebView也无法返回，执行默认的返回操作
                    super.onBackPressed()
                }
            }
        }
    }

    /**
     * 将返回事件传递给WebView处理
     */
    private fun handleBackPressedInWebView(callback: (Boolean) -> Unit) {
        try {
            // 通过JavaScript通知WebView返回事件
            val jsCode = """
                (function() {
                    try {
                        // 触发自定义返回事件
                        var backEvent = new CustomEvent('androidBackPressed', {
                            detail: {
                                timestamp: Date.now(),
                                source: 'android'
                            },
                            bubbles: true,
                            cancelable: true
                        });

                        // 分发事件到document
                        var handled = !document.dispatchEvent(backEvent);

                        // 如果有全局的onAndroidBackPressed函数，也调用它
                        if (typeof window.onAndroidBackPressed === 'function') {
                            try {
                                var result = window.onAndroidBackPressed();
                                if (result === true) {
                                    handled = true;
                                }
                            } catch (e) {
                                console.error('Error in onAndroidBackPressed:', e);
                            }
                        }

                        // 记录处理结果
                        console.log('Android返回事件处理结果:', handled);

                        // 返回是否被处理
                        return handled;
                    } catch (e) {
                        console.error('Error handling Android back pressed:', e);
                        return false;
                    }
                })();
            """.trimIndent()

            webView.evaluateJavascript(jsCode) { result ->
                val handled = result == "true"
                Log.d(TAG, "WebView处理返回事件结果: $handled")
                callback(handled)
            }
        } catch (e: Exception) {
            Log.e(TAG, "传递返回事件到WebView时出错", e)
            callback(false)
        }
    }

    /**
     * 检查控制台消息中是否包含EQ参数或酷安跳转指令
     */
    private fun checkForEQParameters(message: String) {
        // 优先检查酷安跳转指令
        val coolapkMatcher = COOLAPK_PATTERN.matcher(message)
        if (coolapkMatcher.find()) {
            val uid = coolapkMatcher.group(1)
            Log.d(TAG, "检测到酷安跳转指令，UID: $uid")

            // 触发酷安跳转
            jumpToCoolapk(uid)
            return
        }

        // 检查EQ参数（排除酷安跳转指令）
        val eqMatcher = EQ_PATTERN.matcher(message)
        if (eqMatcher.find()) {
            val eqParams = eqMatcher.group(1)

            // 如果包含 "toCoolapk:" 则跳过EQ参数处理
            if (eqParams.contains("toCoolapk:")) {
                Log.d(TAG, "跳过酷安跳转指令的EQ参数处理: $eqParams")
                return
            }

            Log.d(TAG, "检测到EQ参数: $eqParams")

            // 解析EQ参数
            val params = parseEQParameters(eqParams)
            if (params != null) {
                Log.d(TAG, "解析的EQ参数: ${params.contentToString()}")

                // 触发自动化操作
                triggerAutomation(params)
            } else {
                Log.w(TAG, "EQ参数解析失败: $eqParams")
            }
        }
    }

    /**
     * 解析EQ参数字符串
     * 格式: +1,+1,+4,+5,+1,+4
     */
    private fun parseEQParameters(paramString: String): Array<Int>? {
        return try {
            val params = paramString.split(",")
            if (params.size != 6) {
                Log.w(TAG, "EQ参数数量不正确，期望6个，实际${params.size}个")
                return null
            }

            val result = Array(6) { 0 }
            for (i in params.indices) {
                val param = params[i].trim()
                result[i] = when {
                    param.startsWith("+") -> param.substring(1).toInt()
                    else -> param.toInt()
                }

                // 验证参数范围 (-6 到 +6)
                if (result[i] < -6 || result[i] > 6) {
                    Log.w(TAG, "EQ参数超出范围: ${result[i]}")
                    return null
                }
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "解析EQ参数时出错: $paramString", e)
            null
        }
    }

    /**
     * 触发自动化操作
     */
    private fun triggerAutomation(eqParams: Array<Int>) {
        Log.d(TAG, "准备触发自动化操作，EQ参数: ${eqParams.contentToString()}")

        // 检查无障碍权限
        if (!AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
            Log.w(TAG, "无障碍权限未授予，显示权限请求弹窗")
            showPermissionDialog(eqParams)
            return
        }

        // 检查是否需要显示EQ指引
        if (UserManager.shouldShowEQGuide(this)) {
            Log.d(TAG, "需要显示EQ指引，显示指引弹窗")
            showEQGuideDialog(eqParams)
            return
        }

        // 检查设备是否支持EQ自动化
        if (!AccessibilityPermissionHelper.isEQAutomationSupported(this)) {
            Log.w(TAG, "设备不支持EQ自动化")
            val deviceInfo = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
            Toast.makeText(this, "当前设备($deviceInfo)不支持EQ自动化功能", Toast.LENGTH_LONG).show()
            return
        }

        // 显示使用的自动化方式（按优先级检测）
        val hasOppoMelody = AccessibilityPermissionHelper.isOppoMelodyInstalled(this)
        val hasHeytapApp = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)
        val automationMethod = when {
            hasOppoMelody -> "使用OPPO无线耳机App进行自动化"
            hasHeytapApp -> "使用欢律App进行自动化"
            else -> "使用系统蓝牙设置进行自动化（OPPO系列设备）"
        }
        Log.d(TAG, automationMethod)

        // 设置EQ参数并启动自动化
        SeqAccessibilityService.instance?.setCustomEQGains(eqParams)
        SeqAccessibilityService.instance?.startAutomation()

        Toast.makeText(this, "开始自动导入EQ设置（每步最多等待3秒）", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "自动化操作已启动，每个步骤最多等待3秒")
    }

    /**
     * 显示权限请求底部模态框
     */
    private fun showPermissionDialog(eqParams: Array<Int>) {
        // 检查是否已经有权限弹窗在显示，防止重复弹出
        if (isPermissionDialogShowing) {
            Log.d(TAG, "权限弹窗已在显示，跳过重复弹出")
            return
        }

        // 标记权限弹窗正在显示
        isPermissionDialogShowing = true

        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_permission, null)

        // 获取控件引用
        val goSettingsButton = dialogView.findViewById<Button>(R.id.btn_go_settings)
        val alreadySetButton = dialogView.findViewById<Button>(R.id.btn_already_set)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)  // 允许通过手势关闭权限弹窗

        // 关闭按钮点击
        closeButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 重置标志位
            isPermissionDialogShowing = false
            bottomSheetDialog.dismiss()
        }

        // 去设置按钮点击
        goSettingsButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            AccessibilityPermissionHelper.openAccessibilitySettings(this)
        }

        // 我已设置按钮点击
        alreadySetButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 重新检查权限
            if (AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
                // 权限已授予，关闭弹窗并启动自动化
                // 重置标志位
                isPermissionDialogShowing = false
                bottomSheetDialog.dismiss()

                // 显示成功提示
                Toast.makeText(this, "权限验证成功，开始自动化操作", Toast.LENGTH_SHORT).show()

                // 直接继续自动化流程，跳过权限检查避免递归调用
                continueAutomationAfterPermission(eqParams)
            } else {
                // 权限仍未授予，显示提示但不关闭弹窗
                Toast.makeText(this, "权限尚未授予，请先去设置中开启", Toast.LENGTH_LONG).show()

                // 添加震动提示效果
                alreadySetButton.animate()
                    .translationX(-10f)
                    .setDuration(50)
                    .withEndAction {
                        alreadySetButton.animate()
                            .translationX(10f)
                            .setDuration(50)
                            .withEndAction {
                                alreadySetButton.animate()
                                    .translationX(0f)
                                    .setDuration(50)
                                    .start()
                            }
                            .start()
                    }
                    .start()
            }
        }

        // 设置弹窗关闭监听器，确保标志位被重置
        bottomSheetDialog.setOnDismissListener {
            isPermissionDialogShowing = false
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()
    }

    /**
     * 显示EQ指引底部模态框
     */
    private fun showEQGuideDialog(eqParams: Array<Int>) {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_eq_guide, null)

        // 获取控件引用
        val understandButton = dialogView.findViewById<Button>(R.id.btn_understand)
        val dontShowAgainButton = dialogView.findViewById<Button>(R.id.btn_dont_show_again)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)  // 允许通过手势关闭

        // 关闭按钮点击
        closeButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            bottomSheetDialog.dismiss()
        }

        // 我知道了按钮点击
        understandButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 关闭弹窗并继续自动化
            bottomSheetDialog.dismiss()

            // 继续执行自动化操作（跳过EQ指引检查）
            continueAutomationAfterGuide(eqParams)
        }

        // 不再提示按钮点击
        dontShowAgainButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 保存不再提示设置
            UserManager.setDontShowEQGuide(this, true)

            // 关闭弹窗并继续自动化
            bottomSheetDialog.dismiss()

            // 显示提示
            Toast.makeText(this, "已设置不再提示", Toast.LENGTH_SHORT).show()

            // 继续执行自动化操作（跳过EQ指引检查）
            continueAutomationAfterGuide(eqParams)
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()
    }

    /**
     * 在权限验证之后继续自动化操作（跳过权限检查避免递归调用）
     */
    private fun continueAutomationAfterPermission(eqParams: Array<Int>) {
        Log.d(TAG, "权限验证成功，继续自动化操作，EQ参数: ${eqParams.contentToString()}")

        // 检查是否需要显示EQ指引
        if (UserManager.shouldShowEQGuide(this)) {
            Log.d(TAG, "需要显示EQ指引，显示指引弹窗")
            showEQGuideDialog(eqParams)
            return
        }

        // 直接进入自动化流程
        continueAutomationAfterGuide(eqParams)
    }

    /**
     * 在EQ指引之后继续自动化操作
     */
    private fun continueAutomationAfterGuide(eqParams: Array<Int>) {
        // 检查设备是否支持EQ自动化
        if (!AccessibilityPermissionHelper.isEQAutomationSupported(this)) {
            Log.w(TAG, "设备不支持EQ自动化")
            val deviceInfo = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
            Toast.makeText(this, "当前设备($deviceInfo)不支持EQ自动化功能", Toast.LENGTH_LONG).show()
            return
        }

        // 显示使用的自动化方式（按优先级检测）
        val hasOppoMelody = AccessibilityPermissionHelper.isOppoMelodyInstalled(this)
        val hasHeytapApp = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)
        val automationMethod = when {
            hasOppoMelody -> "使用OPPO无线耳机App进行自动化"
            hasHeytapApp -> "使用欢律App进行自动化"
            else -> "使用系统蓝牙设置进行自动化（OPPO系列设备）"
        }
        Log.d(TAG, automationMethod)

        // 设置EQ参数并启动自动化
        SeqAccessibilityService.instance?.setCustomEQGains(eqParams)
        SeqAccessibilityService.instance?.startAutomation()

        Toast.makeText(this, "开始自动导入EQ设置（每步最多等待3秒）", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "自动化操作已启动，每个步骤最多等待3秒")
    }

    @SuppressLint("GestureBackNavigation")
    override fun onResume() {
        super.onResume()
        // 标记应用在前台
        isAppInForeground = true
        // 确保回调设置正确（防止服务重启后回调丢失）
        SeqAccessibilityService.instance?.setAutomationCallback(this)
    }

    override fun onPause() {
        super.onPause()
        // 标记应用不在前台
        isAppInForeground = false
    }





    /**
     * 设置剪贴板监听器
     */
    private fun setupClipboardListener() {
        try {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

            clipboardListener = ClipboardManager.OnPrimaryClipChangedListener {
                Log.d(TAG, "剪贴板内容已更改")
                // 通知WebView剪贴板内容已更新
                notifyWebViewClipboardChanged()
            }

            clipboard.addPrimaryClipChangedListener(clipboardListener)
            Log.d(TAG, "剪贴板监听器已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置剪贴板监听器失败", e)
        }
    }

    /**
     * 通知WebView剪贴板内容已更改
     */
    private fun notifyWebViewClipboardChanged() {
        try {
            val jsCode = """
                if (window.onClipboardChanged && typeof window.onClipboardChanged === 'function') {
                    window.onClipboardChanged();
                }

                // 触发自定义事件
                if (window.dispatchEvent) {
                    var event = new CustomEvent('clipboardChanged');
                    window.dispatchEvent(event);
                }
            """.trimIndent()

            webView.post {
                webView.evaluateJavascript(jsCode) { result ->
                    Log.d(TAG, "剪贴板更改通知已发送到WebView: $result")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "通知WebView剪贴板更改失败", e)
        }
    }

    override fun onDestroy() {
        // 清除页面加载检查任务
        cancelPageLoadCheck()

        // 清除剪贴板监听器
        try {
            clipboardListener?.let { listener ->
                val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                clipboard.removePrimaryClipChangedListener(listener)
                Log.d(TAG, "剪贴板监听器已清除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "清除剪贴板监听器失败", e)
        }

        // 清除回调
        SeqAccessibilityService.instance?.setAutomationCallback(null)
        webView.destroy()
        super.onDestroy()
    }

    /**
     * 自动化完成回调实现
     */
    override fun onAutomationCompleted(eqParams: Array<Int>) {
        runOnUiThread {
            showSuccessMessages(eqParams)
        }
    }

    /**
     * 自动化超时回调实现
     */
    override fun onAutomationTimeout(currentStep: Int, stepDescription: String) {
        runOnUiThread {
            Log.w(TAG, "自动化超时：步骤 $currentStep - $stepDescription")

            // 检查应用是否在前台
            val isInForeground = isInForeground()

            val timeoutMessage = "自动化操作超时（3秒）\n当前步骤：$stepDescription\n\n可能原因：\n• 界面加载较慢\n• 设备性能限制\n• 应用界面发生变化\n\n建议：重试或手动操作"

            if (isInForeground) {
                // 应用在前台，显示Toast和对话框
                Toast.makeText(this, "自动化超时，请重试", Toast.LENGTH_LONG).show()
                showTimeoutDialog(currentStep, stepDescription, timeoutMessage)
            } else {
                // 应用不在前台，显示超时通知
                NotificationHelper.showErrorNotification(this, "自动化超时：$stepDescription")
                Log.d(TAG, "应用不在前台，已显示超时通知: $stepDescription")
            }
        }
    }

    /**
     * 自动化错误回调实现
     */
    override fun onAutomationError(error: String) {
        runOnUiThread {
            Log.e(TAG, "自动化错误：$error")

            // 检查应用是否在前台
            val isInForeground = isInForeground()

            if (isInForeground) {
                // 应用在前台，显示Toast和对话框
                Toast.makeText(this, "自动化操作失败：$error", Toast.LENGTH_LONG).show()
                showAutomationErrorDialog("操作失败", error)
            } else {
                // 应用不在前台，显示错误通知
                NotificationHelper.showErrorNotification(this, error)
                Log.d(TAG, "应用不在前台，已显示错误通知: $error")
            }
        }
    }

    /**
     * 显示超时对话框
     */
    private fun showTimeoutDialog(currentStep: Int, stepDescription: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle("自动化超时")
            .setMessage(message)
            .setPositiveButton("重试") { dialog, _ ->
                dialog.dismiss()
                // 重新触发自动化
                val lastEQParams = SeqAccessibilityService.instance?.getCurrentEQGains()
                if (lastEQParams != null) {
                    triggerAutomation(lastEQParams)
                } else {
                    Toast.makeText(this, "无法获取EQ参数，请重新导入", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("查看帮助") { dialog, _ ->
                dialog.dismiss()
                showTimeoutHelpDialog(currentStep)
            }
            .setCancelable(true)
            .show()
    }

    /**
     * 显示超时帮助对话框
     */
    private fun showTimeoutHelpDialog(currentStep: Int) {
        val helpMessage = when (currentStep) {
            1 -> "步骤1超时：应用启动\n\n解决方案：\n• 确保欢律App或蓝牙设置可以正常打开\n• 关闭其他占用内存的应用\n• 重启设备后重试"
            2 -> "步骤2超时：查找大师调音\n\n解决方案：\n• 确保已连接支持的蓝牙耳机\n• 手动进入大师调音界面\n• 检查耳机是否支持EQ功能"
            3 -> "步骤3超时：查找Seeq选项\n\n解决方案：\n• 确保Seeq配置已正确安装\n• 手动查找并选择Seeq选项\n• 联系技术支持获取最新配置"
            4, 5 -> "步骤${currentStep}超时：EQ界面操作\n\n解决方案：\n• 等待界面完全加载后重试\n• 手动调节EQ参数\n• 检查屏幕分辨率兼容性"
            else -> "步骤${currentStep}超时\n\n通用解决方案：\n• 重启应用后重试\n• 检查无障碍权限\n• 联系技术支持"
        }

        AlertDialog.Builder(this)
            .setTitle("超时帮助")
            .setMessage(helpMessage)
            .setPositiveButton("我知道了") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(true)
            .show()
    }

    /**
     * 显示自动化错误对话框
     */
    private fun showAutomationErrorDialog(title: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定") { dialog, _ ->
                dialog.dismiss()
            }
            .setNegativeButton("重试") { dialog, _ ->
                dialog.dismiss()
                // 重新触发自动化（如果有EQ参数的话）
                val lastEQParams = SeqAccessibilityService.instance?.getCurrentEQGains()
                if (lastEQParams != null) {
                    triggerAutomation(lastEQParams)
                }
            }
            .setCancelable(true)
            .show()
    }

    /**
     * 显示成功消息气泡
     */
    private fun showSuccessMessages(eqParams: Array<Int>) {
        // 格式化EQ参数为字符串
        val eqString = eqParams.joinToString(",") { value ->
            if (value > 0) "+$value" else value.toString()
        }

        // 检查应用是否在前台
        val isInForeground = isInForeground()

        Log.d(TAG, "应用是否在前台: $isInForeground")

        if (isInForeground) {
            // 应用在前台，使用Toast显示消息
            val firstMessage = "EQ应用成功：{$eqString}"
            Toast.makeText(this, firstMessage, Toast.LENGTH_LONG).show()

            Log.d(TAG, "显示第一个成功消息(Toast): $firstMessage")

            // 延迟显示第二个消息
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val secondMessage = "若以上EQ与目前调整EQ的不一致，请切回Seeq再进行一次导入。"
                Toast.makeText(this, secondMessage, Toast.LENGTH_LONG).show()

                Log.d(TAG, "显示第二个成功消息(Toast): $secondMessage")
            }, 3500) // 3.5秒后显示第二个消息，确保第一个消息显示完毕
        } else {
            // 应用不在前台，使用通知显示消息
            NotificationHelper.showSuccessNotification(this, eqParams)
            Log.d(TAG, "应用不在前台，已显示成功通知: $eqString")
        }
    }

    /**
     * 检查并请求必要的权限
     */
    private fun checkAndRequestPermissions(): Boolean {
        val permissions = mutableListOf<String>()

        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.CAMERA)
        }

        // 根据Android版本检查存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用新的媒体权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
            // Android 13+ 检查通知权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        } else {
            // Android 12 及以下使用传统存储权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }

        return if (permissions.isNotEmpty()) {
            Log.d(TAG, "请求权限: ${permissions.joinToString()}")
            permissionLauncher.launch(permissions.toTypedArray())
            false
        } else {
            Log.d(TAG, "所有权限已授予")
            true
        }
    }

    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(permissions: Map<String, Boolean>) {
        val allGranted = permissions.values.all { it }

        if (allGranted) {
            Log.d(TAG, "所有权限已授予，启动文件选择器")
            showFileChooser(null)
        } else {
            Log.w(TAG, "权限被拒绝")
            Toast.makeText(this, "需要相机和存储权限才能上传图片", Toast.LENGTH_LONG).show()

            // 取消文件上传
            fileUploadCallback?.onReceiveValue(null)
            fileUploadCallback = null
        }
    }

    /**
     * 显示文件选择器
     */
    private fun showFileChooser(fileChooserParams: WebChromeClient.FileChooserParams?) {
        try {
            val intents = mutableListOf<Intent>()

            // 创建图片选择Intent
            val galleryIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "image/*"
                putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            }

            // 创建相机拍照Intent
            val cameraIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            if (cameraIntent.resolveActivity(packageManager) != null) {
                // 创建临时文件用于保存拍照结果
                val photoFile = createImageFile()
                if (photoFile != null) {
                    cameraPhotoPath = photoFile.absolutePath
                    val photoURI = FileProvider.getUriForFile(
                        this,
                        "${packageName}.fileprovider",
                        photoFile
                    )
                    cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                    intents.add(cameraIntent)
                }
            }

            // 创建选择器Intent
            val chooserIntent = Intent.createChooser(galleryIntent, "选择图片")
            if (intents.isNotEmpty()) {
                chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, intents.toTypedArray())
            }

            Log.d(TAG, "启动文件选择器")
            fileChooserLauncher.launch(chooserIntent)

        } catch (e: Exception) {
            Log.e(TAG, "启动文件选择器失败", e)
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()

            // 取消文件上传
            fileUploadCallback?.onReceiveValue(null)
            fileUploadCallback = null
        }
    }

    /**
     * 创建临时图片文件
     */
    private fun createImageFile(): File? {
        return try {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFileName = "JPEG_${timeStamp}_"
            val storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            File.createTempFile(imageFileName, ".jpg", storageDir)
        } catch (e: IOException) {
            Log.e(TAG, "创建图片文件失败", e)
            null
        }
    }

    /**
     * 处理文件选择结果
     */
    private fun handleFileChooserResult(resultCode: Int, data: Intent?) {
        val results = mutableListOf<Uri>()

        try {
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    // 处理从图库选择的图片
                    val clipData = data.clipData
                    if (clipData != null) {
                        // 多选图片
                        for (i in 0 until clipData.itemCount) {
                            val uri = clipData.getItemAt(i).uri
                            results.add(uri)
                            Log.d(TAG, "选择的图片: $uri")
                        }
                    } else {
                        // 单选图片
                        data.data?.let { uri ->
                            results.add(uri)
                            Log.d(TAG, "选择的图片: $uri")
                        }
                    }
                } else {
                    // 处理相机拍照结果
                    cameraPhotoPath?.let { path ->
                        val file = File(path)
                        if (file.exists()) {
                            val uri = FileProvider.getUriForFile(
                                this,
                                "${packageName}.fileprovider",
                                file
                            )
                            results.add(uri)
                            Log.d(TAG, "拍照结果: $uri")
                        }
                    }
                }
            }

            Log.d(TAG, "文件选择完成，共选择 ${results.size} 个文件")

        } catch (e: Exception) {
            Log.e(TAG, "处理文件选择结果时出错", e)
        } finally {
            // 将结果传递给WebView
            fileUploadCallback?.onReceiveValue(
                if (results.isNotEmpty()) results.toTypedArray() else null
            )
            fileUploadCallback = null
            cameraPhotoPath = null
        }
    }

    /**
     * 显示登录底部模态框
     */
    private fun showLoginDialog() {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_login, null)

        // 获取控件引用
        val loginCodeInput = dialogView.findViewById<TextInputEditText>(R.id.et_login_code)
        val copyAndJumpButton = dialogView.findViewById<Button>(R.id.btn_copy_and_jump)
        val loginButton = dialogView.findViewById<Button>(R.id.btn_login)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false)

        // 初始状态：登录按钮不可点击
        loginButton.isEnabled = false
        loginButton.alpha = 0.5f

        // 监听登录码输入
        loginCodeInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val loginCode = s?.toString() ?: ""
                val isValid = loginCode.length == 6 && loginCode.all { it.isDigit() }

                loginButton.isEnabled = isValid
                loginButton.alpha = if (isValid) 1.0f else 0.5f

                // 添加输入框动画效果
                if (isValid) {
                    loginCodeInput.animate()
                        .scaleX(1.02f)
                        .scaleY(1.02f)
                        .setDuration(100)
                        .withEndAction {
                            loginCodeInput.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .setDuration(100)
                                .start()
                        }
                        .start()
                }
            }
        })

        // 复制指令并跳转QQ群
        copyAndJumpButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            copyCommandAndJumpToQQ()
        }

        // 登录按钮点击
        loginButton.setOnClickListener {
            val loginCode = loginCodeInput.text.toString()
            if (loginCode.length == 6 && loginCode.all { it.isDigit() }) {
                // 添加按钮点击动画
                it.animate()
                    .scaleX(0.95f)
                    .scaleY(0.95f)
                    .setDuration(100)
                    .withEndAction {
                        it.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start()
                    }
                    .start()

                performLogin(loginCode, bottomSheetDialog)
            } else {
                Toast.makeText(this, "请输入6位数字登录码", Toast.LENGTH_SHORT).show()
            }
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()
    }

    /**
     * 显示网络错误底部模态框
     */
    private fun showNetworkErrorDialog() {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_network_check, null)

        // 获取控件引用
        val networkStatusText = dialogView.findViewById<TextView>(R.id.tv_network_status)
        val retryButton = dialogView.findViewById<Button>(R.id.btn_retry)
        val exitButton = dialogView.findViewById<Button>(R.id.btn_exit)

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false) // 不允许通过手势关闭网络错误弹窗

        // 显示当前网络状态
        val networkType = NetworkHelper.getNetworkTypeDescription(this)
        val hasConnection = NetworkHelper.hasNetworkConnection(this)
        val statusText = if (hasConnection) {
            "已连接到 $networkType，但无法访问Seeq服务器"
        } else {
            "未连接到网络"
        }
        networkStatusText.text = statusText

        // 重试按钮点击
        retryButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 关闭当前对话框并重新检查网络
            bottomSheetDialog.dismiss()
            checkNetworkConnection()
        }

        // 退出按钮点击
        exitButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            // 退出应用
            finishAffinity()
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()
    }

    /**
     * 显示Toast消息的底部模态框
     */
    private fun showToastBottomSheet(message: String, type: String) {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_toast, null)

        // 获取控件引用
        val toastIcon = dialogView.findViewById<ImageView>(R.id.iv_toast_icon)
        val toastMessage = dialogView.findViewById<TextView>(R.id.tv_toast_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 根据类型设置图标和颜色
        when (type.lowercase()) {
            "success" -> {
                toastIcon.setImageResource(android.R.drawable.ic_dialog_info) // 可以替换为自定义成功图标
                toastIcon.setColorFilter(getColor(android.R.color.holo_green_dark))
            }
            "error" -> {
                toastIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                toastIcon.setColorFilter(getColor(android.R.color.holo_red_dark))
            }
            "warning" -> {
                toastIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                toastIcon.setColorFilter(getColor(android.R.color.holo_orange_dark))
            }
            else -> { // info 或其他
                toastIcon.setImageResource(android.R.drawable.ic_dialog_info)
                toastIcon.setColorFilter(getColor(R.color.primary))
            }
        }

        // 设置消息内容
        toastMessage.text = message

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true) // 允许通过手势关闭

        // 确定按钮点击
        okButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            bottomSheetDialog.dismiss()
        }

        // 关闭按钮点击
        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()

        // 自动关闭（可选，模拟toast的自动消失行为）
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            if (bottomSheetDialog.isShowing) {
                bottomSheetDialog.dismiss()
            }
        }, 5000) // 5秒后自动关闭
    }

    /**
     * 显示Alert消息的底部模态框
     */
    private fun showAlertBottomSheet(message: String, title: String, type: String) {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_alert, null)

        // 获取控件引用
        val alertIcon = dialogView.findViewById<ImageView>(R.id.iv_alert_icon)
        val alertTitle = dialogView.findViewById<TextView>(R.id.tv_alert_title)
        val alertMessage = dialogView.findViewById<TextView>(R.id.tv_alert_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 根据类型设置图标、标题和按钮
        when (type.lowercase()) {
            "confirm" -> {
                alertIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                alertIcon.setColorFilter(getColor(android.R.color.holo_orange_dark))
                cancelButton.visibility = android.view.View.VISIBLE
                okButton.text = "确定"
                cancelButton.text = "取消"
            }
            "error" -> {
                alertIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                alertIcon.setColorFilter(getColor(android.R.color.holo_red_dark))
                cancelButton.visibility = android.view.View.GONE
                okButton.text = "确定"
            }
            else -> { // alert 或其他
                alertIcon.setImageResource(android.R.drawable.ic_dialog_info)
                alertIcon.setColorFilter(getColor(R.color.primary))
                cancelButton.visibility = android.view.View.GONE
                okButton.text = "确定"
            }
        }

        // 设置标题和消息内容
        alertTitle.text = title
        alertMessage.text = message

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true) // 允许通过手势关闭

        // 确定按钮点击
        okButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            bottomSheetDialog.dismiss()
        }

        // 取消按钮点击（仅在confirm类型时显示）
        cancelButton.setOnClickListener {
            // 添加按钮点击动画
            it.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()

            bottomSheetDialog.dismiss()
        }

        // 关闭按钮点击
        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        // 显示底部模态框（自定义类会自动完全展开）
        bottomSheetDialog.show()
    }

    /**
     * 显示Confirm消息的底部模态框（带JsResult处理）
     */
    private fun showConfirmBottomSheet(message: String, title: String, result: JsResult?) {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_alert, null)

        // 获取控件引用
        val alertIcon = dialogView.findViewById<ImageView>(R.id.iv_alert_icon)
        val alertTitle = dialogView.findViewById<TextView>(R.id.tv_alert_title)
        val alertMessage = dialogView.findViewById<TextView>(R.id.tv_alert_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 设置confirm样式
        alertIcon.setImageResource(android.R.drawable.ic_dialog_alert)
        alertIcon.setColorFilter(getColor(android.R.color.holo_orange_dark))
        cancelButton.visibility = android.view.View.VISIBLE
        okButton.text = "确定"
        cancelButton.text = "取消"

        // 设置标题和消息内容
        alertTitle.text = title
        alertMessage.text = message

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false) // confirm弹窗不允许通过手势关闭

        // 确定按钮点击
        okButton.setOnClickListener {
            result?.confirm()
            bottomSheetDialog.dismiss()
        }

        // 取消按钮点击
        cancelButton.setOnClickListener {
            result?.cancel()
            bottomSheetDialog.dismiss()
        }

        // 关闭按钮点击
        closeButton.setOnClickListener {
            result?.cancel()
            bottomSheetDialog.dismiss()
        }

        // 显示底部模态框
        bottomSheetDialog.show()
    }

    /**
     * 显示Prompt消息的底部模态框（带JsPromptResult处理）
     */
    private fun showPromptBottomSheet(message: String, title: String, defaultValue: String, result: JsPromptResult?) {
        // 创建底部模态框
        val bottomSheetDialog = FullExpandBottomSheetDialog(this, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_alert, null)

        // 获取控件引用
        val alertIcon = dialogView.findViewById<ImageView>(R.id.iv_alert_icon)
        val alertTitle = dialogView.findViewById<TextView>(R.id.tv_alert_title)
        val alertMessage = dialogView.findViewById<TextView>(R.id.tv_alert_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        // 设置prompt样式
        alertIcon.setImageResource(android.R.drawable.ic_dialog_info)
        alertIcon.setColorFilter(getColor(R.color.primary))
        cancelButton.visibility = android.view.View.VISIBLE
        okButton.text = "确定"
        cancelButton.text = "取消"

        // 设置标题和消息内容
        alertTitle.text = title
        alertMessage.text = message

        // 创建输入框（简化版本，使用TextView显示提示）
        // 注意：这里为了简化，暂时不添加输入框，只显示默认值
        // 如果需要真正的输入功能，需要修改布局文件添加EditText
        if (defaultValue.isNotEmpty()) {
            alertMessage.text = "$message\n\n默认值: $defaultValue"
        }

        // 设置底部模态框内容
        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false) // prompt弹窗不允许通过手势关闭

        // 确定按钮点击
        okButton.setOnClickListener {
            result?.confirm(defaultValue) // 暂时返回默认值
            bottomSheetDialog.dismiss()
        }

        // 取消按钮点击
        cancelButton.setOnClickListener {
            result?.cancel()
            bottomSheetDialog.dismiss()
        }

        // 关闭按钮点击
        closeButton.setOnClickListener {
            result?.cancel()
            bottomSheetDialog.dismiss()
        }

        // 显示底部模态框
        bottomSheetDialog.show()
    }

    /**
     * 复制指令并跳转QQ群
     */
    private fun copyCommandAndJumpToQQ() {
        try {
            // 复制指令到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("QQ群指令", QQ_GROUP_COMMAND)
            clipboard.setPrimaryClip(clip)

            Toast.makeText(this, "指令已复制到剪贴板", Toast.LENGTH_SHORT).show()

            // 尝试跳转QQ群
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(QQ_GROUP_URL))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "无法直接跳转QQ群", e)
                Toast.makeText(this, "请手动打开QQ群并发送已复制的指令", Toast.LENGTH_LONG).show()
            }

        } catch (e: Exception) {
            Log.e(TAG, "复制指令失败", e)
            Toast.makeText(this, "复制指令失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 执行登录
     */
    private fun performLogin(loginCode: String, dialog: BottomSheetDialog) {
        Log.d(TAG, "开始执行登录，登录码: $loginCode")

        // 显示加载状态
        val loginButton = dialog.findViewById<Button>(R.id.btn_login)
        loginButton?.let {
            it.isEnabled = false
            it.text = "登录中..."

            // 添加加载动画
            it.animate()
                .alpha(0.7f)
                .setDuration(200)
                .start()
        }

        // 执行登录请求
        LoginService.login(loginCode, object : LoginCallback {
            override fun onSuccess(authToken: String) {
                runOnUiThread {
                    Log.d(TAG, "登录成功，authToken: $authToken")

                    // 保存authToken
                    UserManager.saveUserID(this@MainActivity, authToken)

                    // 关闭弹窗
                    dialog.dismiss()

                    // 显示成功提示
                    Toast.makeText(this@MainActivity, "登录成功！", Toast.LENGTH_SHORT).show()

                    // 加载WebView
                    loadWebViewWithUserID(authToken)
                }
            }

            override fun onError(message: String) {
                runOnUiThread {
                    Log.w(TAG, "登录失败: $message")

                    // 恢复按钮状态
                    loginButton?.let {
                        it.isEnabled = true
                        it.text = "登录"

                        // 恢复按钮透明度动画
                        it.animate()
                            .alpha(1.0f)
                            .setDuration(200)
                            .start()
                    }

                    // 显示错误提示
                    Toast.makeText(this@MainActivity, "登录失败: $message", Toast.LENGTH_LONG).show()
                }
            }
        })
    }

    /**
     * 跳转到酷安
     */
    private fun jumpToCoolapk(uid: String?) {
        if (uid.isNullOrBlank()) {
            Log.w(TAG, "酷安UID为空，跳转失败")
            return
        }

        Log.d(TAG, "准备跳转酷安，UID: $uid")

        try {
            // 首先尝试使用酷安App的scheme跳转
            val appIntent = Intent(Intent.ACTION_VIEW, Uri.parse("coolmarket://u/$uid"))
            appIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            // 检查是否有应用可以处理这个Intent
            if (appIntent.resolveActivity(packageManager) != null) {
                Log.d(TAG, "使用酷安App跳转")
                startActivity(appIntent)
                return
            }

            // 如果酷安App未安装，则跳转到网页版
            Log.d(TAG, "酷安App未安装，跳转到网页版")
            val webIntent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.coolapk.com/u/$uid"))
            webIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(webIntent)

        } catch (e: Exception) {
            Log.e(TAG, "跳转酷安失败", e)
            // 静默处理失败，不显示任何提示（按照要求）
        }
    }

    /**
     * 测试底部模态框动画效果
     * 用于验证动画修复是否有效
     */
    private fun testBottomSheetAnimations() {
        Log.d(TAG, "开始测试底部模态框动画")

        // 测试Toast模态框
        showToastBottomSheet("这是一个测试Toast消息，用于验证底部弹出和关闭动画是否正常工作", "info")

        // 延迟测试其他模态框，避免重叠
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            // 可以在这里添加其他模态框的测试
            Log.d(TAG, "底部模态框动画测试完成")
        }, 6000)
    }
}

