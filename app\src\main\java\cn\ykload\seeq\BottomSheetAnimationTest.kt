package cn.ykload.seeq

import android.content.Context
import android.view.LayoutInflater
import android.widget.Button
import android.widget.TextView
import android.widget.Toast

/**
 * 底部模态框动画测试工具类
 * 用于测试各种底部模态框的动画效果
 */
object BottomSheetAnimationTest {

    /**
     * 测试权限申请模态框动画
     */
    fun testPermissionDialog(context: Context) {
        val bottomSheetDialog = FullExpandBottomSheetDialog(context, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(context).inflate(R.layout.bottom_sheet_permission, null)

        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)
        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)
        bottomSheetDialog.show()
    }

    /**
     * 测试登录模态框动画
     */
    fun testLoginDialog(context: Context) {
        val bottomSheetDialog = FullExpandBottomSheetDialog(context, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(context).inflate(R.layout.bottom_sheet_login, null)

        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(false)
        bottomSheetDialog.show()
    }

    /**
     * 测试Toast模态框动画
     */
    fun testToastDialog(context: Context) {
        val bottomSheetDialog = FullExpandBottomSheetDialog(context, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(context).inflate(R.layout.bottom_sheet_toast, null)

        val toastMessage = dialogView.findViewById<TextView>(R.id.tv_toast_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        toastMessage.text = "这是一个测试Toast消息"

        okButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)
        bottomSheetDialog.show()
    }

    /**
     * 测试Alert模态框动画
     */
    fun testAlertDialog(context: Context) {
        val bottomSheetDialog = FullExpandBottomSheetDialog(context, R.style.Theme_Seeq_BottomSheetDialog)
        val dialogView = LayoutInflater.from(context).inflate(R.layout.bottom_sheet_alert, null)

        val alertTitle = dialogView.findViewById<TextView>(R.id.tv_alert_title)
        val alertMessage = dialogView.findViewById<TextView>(R.id.tv_alert_message)
        val okButton = dialogView.findViewById<Button>(R.id.btn_ok)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val closeButton = dialogView.findViewById<android.widget.ImageButton>(R.id.btn_close)

        alertTitle.text = "测试Alert标题"
        alertMessage.text = "这是一个测试Alert消息"

        okButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        cancelButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        closeButton.setOnClickListener {
            bottomSheetDialog.dismiss()
        }

        bottomSheetDialog.setContentView(dialogView)
        bottomSheetDialog.setCancelable(true)
        bottomSheetDialog.show()
    }

    /**
     * 显示动画测试菜单
     */
    fun showTestMenu(context: Context) {
        Toast.makeText(context, "底部模态框动画测试已准备就绪", Toast.LENGTH_SHORT).show()
    }
}
