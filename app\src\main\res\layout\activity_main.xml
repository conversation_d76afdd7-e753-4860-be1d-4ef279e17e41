<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"

    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 启动加载图层 -->
    <LinearLayout
        android:id="@+id/splash_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@android:color/white"
        android:elevation="10dp">

        <!-- Logo容器 -->
        <LinearLayout
            android:id="@+id/logo_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="48dp">

            <!-- App Logo -->
            <ImageView
                android:id="@+id/app_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/seeq_logo"
                android:background="@drawable/rounded_image_background"
                android:clipToOutline="true"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:maxWidth="200dp"
                android:maxHeight="200dp"
                android:layout_marginBottom="24dp" />

            <!-- 副标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="See EQs uLike"
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:alpha="0.8" />

        </LinearLayout>

        <!-- 加载指示器容器 -->
        <LinearLayout
            android:id="@+id/loading_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- 加载动画 -->
            <ProgressBar
                android:id="@+id/loading_progress"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:indeterminateTint="@color/primary"
                android:layout_marginBottom="16dp" />

            <!-- 加载文本 -->
            <TextView
                android:id="@+id/loading_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="少女祈祷中..."
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:alpha="0.7" />

        </LinearLayout>

        <!-- 底部版本信息 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="32dp"
            android:alpha="0.5">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Powered by "
                android:textSize="12sp"
                android:textColor="@color/text_hint" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="YKload"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/primary" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
