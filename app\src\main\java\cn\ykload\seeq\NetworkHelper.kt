package cn.ykload.seeq

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import kotlinx.coroutines.*
import java.io.IOException
import java.net.InetSocketAddress
import java.net.Socket

/**
 * 网络连接检测工具类
 */
object NetworkHelper {
    
    private const val TAG = "NetworkHelper"
    private const val SEEQ_HOST = "seeq.ykload.com"
    private const val SEEQ_PORT = 443 // HTTPS端口
    private const val CONNECTION_TIMEOUT = 10000 // 10秒超时
    
    /**
     * 网络检测回调接口
     */
    interface NetworkCheckCallback {
        fun onNetworkAvailable()
        fun onNetworkUnavailable()
    }
    
    /**
     * 检查设备是否有网络连接
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 检查是否能连接到Seeq服务器
     * 使用协程异步执行，避免阻塞主线程
     */
    fun checkSeeqConnection(callback: NetworkCheckCallback) {
        Log.d(TAG, "开始检查Seeq服务器连接...")
        
        // 使用IO调度器执行网络操作
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val isConnected = pingSeeqServer()
                
                // 切换到主线程回调结果
                withContext(Dispatchers.Main) {
                    if (isConnected) {
                        Log.d(TAG, "Seeq服务器连接成功")
                        callback.onNetworkAvailable()
                    } else {
                        Log.w(TAG, "Seeq服务器连接失败")
                        callback.onNetworkUnavailable()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查Seeq服务器连接时出错", e)
                
                // 切换到主线程回调错误
                withContext(Dispatchers.Main) {
                    callback.onNetworkUnavailable()
                }
            }
        }
    }
    
    /**
     * 通过Socket连接测试服务器可达性
     * 这比真正的ping更可靠，因为Android限制了ICMP ping
     */
    private suspend fun pingSeeqServer(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "尝试连接 $SEEQ_HOST:$SEEQ_PORT")
            
            val socket = Socket()
            socket.use {
                // 设置连接超时
                it.connect(InetSocketAddress(SEEQ_HOST, SEEQ_PORT), CONNECTION_TIMEOUT)
                Log.d(TAG, "成功连接到 $SEEQ_HOST:$SEEQ_PORT")
                true
            }
        } catch (e: IOException) {
            Log.w(TAG, "连接 $SEEQ_HOST:$SEEQ_PORT 失败: ${e.message}")
            false
        } catch (e: Exception) {
            Log.e(TAG, "连接测试出现异常", e)
            false
        }
    }
    
    /**
     * 获取网络类型描述
     */
    fun getNetworkTypeDescription(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
            
            return when {
                networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "移动网络"
                networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "以太网"
                else -> "未知网络"
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return when (networkInfo?.type) {
                ConnectivityManager.TYPE_WIFI -> "WiFi"
                ConnectivityManager.TYPE_MOBILE -> "移动网络"
                ConnectivityManager.TYPE_ETHERNET -> "以太网"
                else -> "未知网络"
            }
        }
    }
    
    /**
     * 同步检查网络连接（用于快速检查）
     * 注意：这只检查设备网络状态，不检查服务器可达性
     */
    fun hasNetworkConnection(context: Context): Boolean {
        return isNetworkAvailable(context)
    }
}
