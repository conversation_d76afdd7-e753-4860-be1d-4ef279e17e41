package cn.ykload.seeq

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

/**
 * 通知帮助类，用于在应用不在前台时显示通知
 */
object NotificationHelper {
    
    private const val TAG = "NotificationHelper"
    
    // 通知渠道ID和名称
    private const val CHANNEL_ID = "seeq_automation"
    private const val CHANNEL_NAME = "Seeq自动化通知"
    private const val CHANNEL_DESCRIPTION = "显示EQ自动化操作的结果通知"
    
    // 通知ID
    private const val NOTIFICATION_ID_SUCCESS = 1001
    private const val NOTIFICATION_ID_ERROR = 1002
    
    /**
     * 初始化通知渠道
     */
    fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableVibration(true)
                setShowBadge(true)
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            Log.d(TAG, "通知渠道已创建: $CHANNEL_NAME")
        }
    }
    
    /**
     * 显示EQ应用成功的通知
     */
    fun showSuccessNotification(context: Context, eqParams: Array<Int>) {
        try {
            // 格式化EQ参数为字符串
            val eqString = eqParams.joinToString(",") { value ->
                if (value > 0) "+$value" else value.toString()
            }
            
            // 创建点击通知时的Intent
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            
            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 构建通知
            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification) // 需要添加这个图标
                .setContentTitle("EQ应用成功")
                .setContentText("EQ参数：{$eqString}")
                .setStyle(
                    NotificationCompat.BigTextStyle()
                        .bigText("EQ应用成功：{$eqString}\n\n若以上EQ与目前调整EQ的不一致，请切回Seeq再进行一次导入。")
                )
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setVibrate(longArrayOf(0, 300, 200, 300)) // 振动模式
                .build()
            
            // 显示通知
            val notificationManager = NotificationManagerCompat.from(context)
            if (notificationManager.areNotificationsEnabled()) {
                notificationManager.notify(NOTIFICATION_ID_SUCCESS, notification)
                Log.d(TAG, "成功通知已显示: $eqString")
            } else {
                Log.w(TAG, "通知权限未授予，无法显示通知")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "显示成功通知失败", e)
        }
    }
    
    /**
     * 显示错误通知
     */
    fun showErrorNotification(context: Context, errorMessage: String) {
        try {
            // 创建点击通知时的Intent
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            
            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 构建通知
            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("EQ自动化失败")
                .setContentText(errorMessage)
                .setStyle(
                    NotificationCompat.BigTextStyle()
                        .bigText("EQ自动化操作失败：$errorMessage")
                )
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setVibrate(longArrayOf(0, 500, 200, 500)) // 更强的振动
                .build()
            
            // 显示通知
            val notificationManager = NotificationManagerCompat.from(context)
            if (notificationManager.areNotificationsEnabled()) {
                notificationManager.notify(NOTIFICATION_ID_ERROR, notification)
                Log.d(TAG, "错误通知已显示: $errorMessage")
            } else {
                Log.w(TAG, "通知权限未授予，无法显示通知")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "显示错误通知失败", e)
        }
    }
    

    
    /**
     * 检查通知权限是否已授予
     */
    fun areNotificationsEnabled(context: Context): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
}
