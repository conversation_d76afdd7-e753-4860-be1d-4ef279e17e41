# EQ指引模态框功能

## 功能概述

在执行自动化操作导入EQ设置前，会弹出一个底部模态框，提示用户在"欢律"耳机的EQ设置界面中添加一个命名为"Seeq"的自定义调音，确保能够成功导入EQ设置。

## 功能特性

### 🎯 触发时机
- 在用户点击导入EQ设置时
- 在无障碍权限检查通过后
- 在设备支持检查之前
- 只有在用户未设置"不再提示"时才显示

### 📱 界面设计
- **样式参考**：无障碍权限申请模态框
- **布局**：底部模态框，支持手势拖拽
- **图标**：EQ均衡器图标
- **标题**：准备导入EQ设置

### 📋 操作指引
模态框包含详细的操作步骤：

1. **步骤1**：打开"欢律"耳机App
2. **步骤2**：进入"大师调音"→"EQ设置"界面  
3. **步骤3**：添加一个命名为"Seeq"的自定义调音

### 🔘 按钮功能

#### 我知道了按钮
- **样式**：主要按钮（Material3.Button）
- **图标**：勾选图标
- **功能**：
  - 关闭模态框
  - 继续执行自动化操作
  - 不保存任何设置

#### 不再提示按钮  
- **样式**：轮廓按钮（Material3.Button.OutlinedButton）
- **图标**：隐藏图标
- **功能**：
  - 保存"不再提示"设置到SharedPreferences
  - 关闭模态框
  - 继续执行自动化操作
  - 显示"已设置不再提示"提示

## 技术实现

### 🗄️ 数据存储
使用`UserManager`类管理用户偏好设置：

```kotlin
// 保存"不再提示"设置
UserManager.setDontShowEQGuide(context, true)

// 检查是否应该显示指引
UserManager.shouldShowEQGuide(context): Boolean
```

### 🔧 SharedPreferences键值
- **键名**：`dont_show_eq_guide`
- **类型**：Boolean
- **默认值**：false（默认显示指引）

### 📱 界面组件

#### 布局文件
- **文件**：`bottom_sheet_eq_guide.xml`
- **基础**：参考`bottom_sheet_permission.xml`
- **组件**：
  - 拖拽手柄
  - 标题区域（图标+标题+关闭按钮）
  - 操作指引卡片
  - 重要提示区域
  - 按钮区域

#### 样式资源
- **步骤数字背景**：`step_number_background.xml`
- **EQ图标**：`ic_equalizer.xml`
- **隐藏图标**：`ic_visibility_off.xml`

### 🔄 执行流程

```
用户点击导入EQ
    ↓
检查无障碍权限
    ↓
检查是否显示EQ指引 ← 新增步骤
    ↓
显示EQ指引模态框 ← 新增功能
    ↓
用户选择操作
    ↓
继续自动化流程
```

### 📝 代码结构

#### MainActivity新增方法
1. **showEQGuideDialog(eqParams: Array<Int>)**
   - 创建并显示EQ指引模态框
   - 处理按钮点击事件
   - 管理动画效果

2. **continueAutomationAfterGuide(eqParams: Array<Int>)**
   - 在指引后继续执行自动化
   - 避免重复检查EQ指引
   - 保持原有的设备支持检查逻辑

#### UserManager新增方法
1. **setDontShowEQGuide(context, dontShow: Boolean)**
   - 保存用户的"不再提示"偏好

2. **shouldShowEQGuide(context): Boolean**
   - 检查是否应该显示EQ指引
   - 返回true表示需要显示

## 用户体验

### ✅ 优点
- **引导性强**：清晰的步骤指引
- **用户友好**：可选择不再提示
- **一致性**：与现有权限弹窗风格统一
- **非侵入性**：不影响已熟悉用户的使用

### 🎨 视觉设计
- **Material Design 3**：遵循现代设计规范
- **步骤数字**：圆形背景，清晰易读
- **颜色主题**：与应用整体风格保持一致
- **动画效果**：按钮点击反馈，提升交互体验

## 测试要点

### 🧪 功能测试
1. **首次使用**：确认模态框正常显示
2. **我知道了**：确认可以继续自动化
3. **不再提示**：确认设置保存成功
4. **再次使用**：确认不再显示模态框
5. **关闭按钮**：确认可以取消操作

### 🔄 边界测试
1. **权限未授予**：确认先显示权限弹窗
2. **设备不支持**：确认在指引后正确提示
3. **数据持久化**：确认重启应用后设置仍有效

## 维护说明

### 📁 相关文件
- `MainActivity.kt`：主要逻辑实现
- `UserManager.kt`：数据存储管理
- `bottom_sheet_eq_guide.xml`：界面布局
- `step_number_background.xml`：步骤数字样式
- `ic_equalizer.xml`：EQ图标
- `ic_visibility_off.xml`：隐藏图标

### 🔧 配置项
- 可通过修改`UserManager`中的键名来调整存储位置
- 可通过修改布局文件来调整界面样式
- 可通过修改颜色资源来调整主题色彩
