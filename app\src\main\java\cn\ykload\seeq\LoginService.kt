package cn.ykload.seeq

import android.util.Log
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException

/**
 * 登录请求数据类
 */
data class LoginRequest(
    @SerializedName("token")
    val token: String,
    @SerializedName("loginCode")
    val loginCode: String
)

/**
 * 登录响应数据类
 */
data class LoginResponse(
    @SerializedName("success")
    val success: Bo<PERSON>an,
    @SerializedName("authToken")
    val authToken: String? = null,
    @SerializedName("message")
    val message: String? = null
)

/**
 * 登录回调接口
 */
interface LoginCallback {
    fun onSuccess(authToken: String)
    fun onError(message: String)
}

/**
 * 登录服务类
 */
object LoginService {
    
    private const val TAG = "LoginService"
    private const val LOGIN_URL = "https://seeq.ykload.com/api/login"
    private const val LOGIN_TOKEN = "SeeqYKload233LOGIN"
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    private val gson = Gson()
    
    /**
     * 执行登录请求
     */
    fun login(loginCode: String, callback: LoginCallback) {
        Log.d(TAG, "开始登录请求，登录码: $loginCode")
        
        // 验证登录码格式
        if (!isValidLoginCode(loginCode)) {
            callback.onError("登录码格式错误，请输入6位数字")
            return
        }
        
        try {
            // 构建请求数据
            val loginRequest = LoginRequest(
                token = LOGIN_TOKEN,
                loginCode = loginCode
            )
            
            val requestJson = gson.toJson(loginRequest)
            Log.d(TAG, "请求数据: $requestJson")
            
            val requestBody = requestJson.toRequestBody("application/json".toMediaType())
            
            // 构建HTTP请求
            val request = Request.Builder()
                .url(LOGIN_URL)
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("User-Agent", "SeeqApp/1.0")
                .build()
            
            // 异步执行请求
            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "登录请求失败", e)
                    callback.onError("网络请求失败: ${e.message}")
                }
                
                override fun onResponse(call: Call, response: Response) {
                    try {
                        val responseBody = response.body?.string()
                        Log.d(TAG, "登录响应: HTTP ${response.code}, Body: $responseBody")
                        
                        if (response.isSuccessful && responseBody != null) {
                            val loginResponse = gson.fromJson(responseBody, LoginResponse::class.java)
                            
                            if (loginResponse.success && !loginResponse.authToken.isNullOrEmpty()) {
                                Log.d(TAG, "登录成功，authToken: ${loginResponse.authToken}")
                                callback.onSuccess(loginResponse.authToken)
                            } else {
                                val errorMsg = loginResponse.message ?: "登录失败，请检查登录码是否正确"
                                Log.w(TAG, "登录失败: $errorMsg")
                                callback.onError(errorMsg)
                            }
                        } else {
                            val errorMsg = "服务器响应错误: HTTP ${response.code}"
                            Log.w(TAG, errorMsg)
                            callback.onError(errorMsg)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析登录响应失败", e)
                        callback.onError("响应解析失败: ${e.message}")
                    } finally {
                        response.close()
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "构建登录请求失败", e)
            callback.onError("请求构建失败: ${e.message}")
        }
    }
    
    /**
     * 验证登录码格式
     */
    private fun isValidLoginCode(loginCode: String): Boolean {
        return loginCode.length == 6 && loginCode.all { it.isDigit() }
    }
}
