# 用户登录系统

## 概述

Seeq应用现已集成完整的用户登录系统，支持通过QQ群获取登录码进行用户认证。

## 功能流程

### 🔐 登录检查流程

```
应用启动
    ↓
检查本地authToken
    ↓
已有authToken → 设置User-Agent → 加载WebView
    ↓
无authToken → 显示登录弹窗
```

### 📱 登录弹窗功能

#### 弹窗内容
- **标题**：用户登录
- **提示信息**：当前未登录，需要去QQ群中粘贴并发送指定文本来获取登录码
- **登录码输入框**：6位纯数字输入
- **复制指令按钮**：复制指令并跳转去Q群中发送
- **登录按钮**：输入完登录码后可点击（输入前为灰色不可点击）

#### 交互逻辑
1. **输入验证**：实时检查登录码格式（6位数字）
2. **按钮状态**：登录码有效时登录按钮才可点击
3. **复制跳转**：一键复制指令并尝试跳转QQ群
4. **登录处理**：网络请求登录并处理结果

## 技术实现

### 🗄️ 数据存储 (UserManager)

```kotlin
object UserManager {
    // 保存authToken到SharedPreferences
    fun saveUserID(context: Context, authToken: String)
    
    // 获取本地保存的authToken
    fun getUserID(context: Context): String?
    
    // 检查是否已登录（authToken存在且为16位数字）
    fun isLoggedIn(context: Context): Boolean
    
    // 验证authToken格式
    fun isValidUserID(authToken: String?): Boolean
}
```

### 🌐 网络请求 (LoginService)

#### 登录API
- **URL**: `https://seeq.ykload.com/api/login`
- **方法**: POST
- **Content-Type**: application/json

#### 请求格式
```json
{
  "token": "SeeqYKload233LOGIN",
  "loginCode": "123456"
}
```

#### 响应格式
```json
{
  "success": true,
  "authToken": "1234567890123456"
}
```

#### 错误处理
- 网络请求失败
- 服务器响应错误
- 登录码格式错误
- 登录验证失败

### 🎨 用户界面

#### 登录弹窗布局 (dialog_login.xml)
- Material Design 3 风格
- TextInputLayout 输入框
- 两个按钮（OutlinedButton + Button）
- 响应式布局

#### 状态管理
- 输入框文本监听
- 按钮启用/禁用状态
- 加载状态显示

## 安全特性

### 🔒 数据安全
- **本地存储**：使用SharedPreferences安全存储
- **网络传输**：HTTPS加密传输
- **输入验证**：严格的格式验证

### 🛡️ 权限控制
- **authToken验证**：16位数字格式验证
- **登录码验证**：6位数字格式验证
- **Token验证**：固定Token防止非法请求

## QQ群集成

### 📋 指令复制
- **指令内容**：`申请Seeq登录码`
- **复制机制**：使用ClipboardManager
- **用户提示**：复制成功提示

### 🔗 群跳转
- **跳转URL**：`mqqapi://card/show_pslcard?...`
- **兼容处理**：跳转失败时的友好提示
- **用户引导**：手动操作指引

## User-Agent设置

### 🏷️ 格式
```
原始User-Agent SeeqApp/1.0 authToken/1234567890123456
```

### 📡 用途
- 服务器端用户识别
- 个性化内容推送
- 用户行为分析

## 错误处理

### 🚨 常见错误
1. **网络连接失败**
   - 提示：网络请求失败
   - 处理：恢复按钮状态

2. **登录码错误**
   - 提示：登录码格式错误或无效
   - 处理：保持弹窗开启

3. **服务器错误**
   - 提示：服务器响应错误
   - 处理：记录日志并提示用户

4. **QQ群跳转失败**
   - 提示：请手动打开QQ群
   - 处理：指令已复制到剪贴板

## 日志记录

### 📝 关键日志
- 登录状态检查
- authToken保存/获取
- 网络请求详情
- 错误异常信息

### 🔍 调试命令
```bash
adb logcat -s MainActivity UserManager LoginService
```

## 测试建议

### ✅ 功能测试
1. 首次启动（无authToken）
2. 已登录启动（有authToken）
3. 登录码输入验证
4. 网络请求成功/失败
5. QQ群跳转功能

### 🔄 边界测试
1. 无网络环境
2. 服务器异常
3. 非法登录码
4. authToken格式异常
5. 存储权限异常

### 📱 兼容性测试
1. 不同Android版本
2. 不同设备厂商
3. 不同QQ版本
4. 不同网络环境
