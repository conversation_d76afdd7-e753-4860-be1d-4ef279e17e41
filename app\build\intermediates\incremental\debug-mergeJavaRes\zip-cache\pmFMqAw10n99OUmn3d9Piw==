[{"key": "androidx/compose/material/ActualJvm_jvmKt.class", "name": "androidx/compose/material/ActualJvm_jvmKt.class", "size": 541, "crc": 252107025}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "size": 2241, "crc": -1006247055}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2.class", "size": 7736, "crc": -764698320}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$3.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$3.class", "size": 2472, "crc": -2077642606}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "size": 3284, "crc": -1019724258}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "size": 3748, "crc": -1121703387}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "size": 3279, "crc": -182230668}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "size": 3761, "crc": -111186098}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "size": 11424, "crc": 1882563804}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$2.class", "size": 2945, "crc": 989490501}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "size": 5575, "crc": -295404261}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "size": 7085, "crc": -2040260672}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$2.class", "size": 2086, "crc": 963359711}, {"key": "androidx/compose/material/AlertDialogKt.class", "name": "androidx/compose/material/AlertDialogKt.class", "size": 23212, "crc": 1434300464}, {"key": "androidx/compose/material/AnchoredDragFinishedSignal.class", "name": "androidx/compose/material/AnchoredDragFinishedSignal.class", "size": 1787, "crc": 1351486909}, {"key": "androidx/compose/material/AnchoredDragScope.class", "name": "androidx/compose/material/AnchoredDragScope.class", "size": 1023, "crc": -1881102352}, {"key": "androidx/compose/material/AnchoredDraggableDefaults.class", "name": "androidx/compose/material/AnchoredDraggableDefaults.class", "size": 1567, "crc": 1158243881}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3703, "crc": 612129614}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 4084, "crc": 1365227966}, {"key": "androidx/compose/material/AnchoredDraggableKt$animateTo$2$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$animateTo$2$1.class", "size": 1858, "crc": -543462057}, {"key": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "size": 5114, "crc": -967027597}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "size": 1666, "crc": -220256676}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4233, "crc": -1313446333}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2080, "crc": 467682347}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "size": 4522, "crc": 1475033890}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "size": 4656, "crc": -766258825}, {"key": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "size": 3868, "crc": -1168745014}, {"key": "androidx/compose/material/AnchoredDraggableKt.class", "name": "androidx/compose/material/AnchoredDraggableKt.class", "size": 10202, "crc": -515628795}, {"key": "androidx/compose/material/AnchoredDraggableState$1.class", "name": "androidx/compose/material/AnchoredDraggableState$1.class", "size": 1603, "crc": -177069669}, {"key": "androidx/compose/material/AnchoredDraggableState$2.class", "name": "androidx/compose/material/AnchoredDraggableState$2.class", "size": 1647, "crc": -847446796}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$1.class", "size": 2280, "crc": 762805626}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$2.class", "size": 3065, "crc": 1600408068}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion.class", "size": 3034, "crc": -873760923}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "size": 1920, "crc": 1320851022}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1741, "crc": -1431337346}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4494, "crc": -1192314209}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "size": 4461, "crc": 1469528848}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "size": 1939, "crc": -1269857098}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 1959, "crc": 565554343}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4724, "crc": 2091563420}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "size": 4698, "crc": -637109670}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1606, "crc": -402088151}, {"key": "androidx/compose/material/AnchoredDraggableState$closestValue$2.class", "name": "androidx/compose/material/AnchoredDraggableState$closestValue$2.class", "size": 2028, "crc": -167898241}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5420, "crc": -12940837}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 1921, "crc": -1765109425}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "size": 3811, "crc": 1330338333}, {"key": "androidx/compose/material/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/material/AnchoredDraggableState$progress$2.class", "size": 2243, "crc": 479211820}, {"key": "androidx/compose/material/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/material/AnchoredDraggableState$targetValue$2.class", "size": 2010, "crc": -1380944883}, {"key": "androidx/compose/material/AnchoredDraggableState$trySnapTo$1.class", "name": "androidx/compose/material/AnchoredDraggableState$trySnapTo$1.class", "size": 2417, "crc": -1376818130}, {"key": "androidx/compose/material/AnchoredDraggableState.class", "name": "androidx/compose/material/AnchoredDraggableState.class", "size": 24663, "crc": 1203536321}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1$1$1.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1$1$1.class", "size": 3094, "crc": 8111188}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1.class", "size": 10799, "crc": 1592440649}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$2.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$2.class", "size": 3717, "crc": -817972818}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$3.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$3.class", "size": 4042, "crc": -1326755007}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$4.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$4.class", "size": 3481, "crc": 656100163}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "size": 11113, "crc": -641916269}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$1.class", "size": 2903, "crc": 17669109}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "size": 4325, "crc": -165127001}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$3.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$3.class", "size": 3123, "crc": -**********}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "size": 2547, "crc": -779049001}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenuItem$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenuItem$1.class", "size": 3190, "crc": **********}, {"key": "androidx/compose/material/AndroidMenu_androidKt.class", "name": "androidx/compose/material/AndroidMenu_androidKt.class", "size": 16757, "crc": **********}, {"key": "androidx/compose/material/AppBarDefaults.class", "name": "androidx/compose/material/AppBarDefaults.class", "size": 5202, "crc": **********}, {"key": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "size": 10373, "crc": 834227551}, {"key": "androidx/compose/material/AppBarKt$AppBar$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1.class", "size": 4362, "crc": 574446412}, {"key": "androidx/compose/material/AppBarKt$AppBar$2.class", "name": "androidx/compose/material/AppBarKt$AppBar$2.class", "size": 3137, "crc": 2118071270}, {"key": "androidx/compose/material/AppBarKt$BottomAppBar$1.class", "name": "androidx/compose/material/AppBarKt$BottomAppBar$1.class", "size": 3131, "crc": -421024107}, {"key": "androidx/compose/material/AppBarKt$BottomAppBar$2.class", "name": "androidx/compose/material/AppBarKt$BottomAppBar$2.class", "size": 2879, "crc": 1841465361}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "size": 3434, "crc": 569699867}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "size": 9479, "crc": -1844321021}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "size": 14781, "crc": 433363584}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$2.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$2.class", "size": 3255, "crc": -748362177}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$3.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$3.class", "size": 3002, "crc": 624246965}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$4.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$4.class", "size": 2922, "crc": 1484771634}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$5.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$5.class", "size": 2670, "crc": -609561142}, {"key": "androidx/compose/material/AppBarKt.class", "name": "androidx/compose/material/AppBarKt.class", "size": 29130, "crc": 728497733}, {"key": "androidx/compose/material/BackdropLayers.class", "name": "androidx/compose/material/BackdropLayers.class", "size": 1396, "crc": -2067388346}, {"key": "androidx/compose/material/BackdropScaffoldDefaults.class", "name": "androidx/compose/material/BackdropScaffoldDefaults.class", "size": 6200, "crc": 1107464088}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1$1.class", "size": 1975, "crc": -1252402018}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1.class", "size": 4438, "crc": 1097638825}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$2$1.class", "size": 3403, "crc": -135955656}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1$1.class", "size": 1976, "crc": -1874389913}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1.class", "size": 4439, "crc": 560471455}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$5$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$5$1.class", "size": 3404, "crc": -2079283986}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$2.class", "size": 2448, "crc": -2048133968}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$1$1.class", "size": 1790, "crc": -1219565282}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$WhenMappings.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$WhenMappings.class", "size": 1035, "crc": -1097694617}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$newAnchors$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$newAnchors$1.class", "size": 2551, "crc": 606757893}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1.class", "size": 3890, "crc": 949209064}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "size": 3856, "crc": 1254413004}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1.class", "size": 2610, "crc": 1123749874}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "size": 3857, "crc": -1853584914}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2.class", "size": 2611, "crc": -1605594575}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1.class", "size": 2564, "crc": -2114212693}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "size": 3847, "crc": -430285476}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1.class", "size": 2556, "crc": -1798810505}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "size": 11789, "crc": -1120466577}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "size": 16595, "crc": -1054562809}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "size": 9613, "crc": -31640470}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$3.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$3.class", "size": 4348, "crc": -289524349}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "size": 11007, "crc": 94513494}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$calculateBackLayerConstraints$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$calculateBackLayerConstraints$1$1.class", "size": 2039, "crc": -1179875042}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffoldState$1.class", "size": 1890, "crc": -143245791}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$2.class", "size": 3453, "crc": -599777519}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "size": 3271, "crc": -1608507178}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1.class", "size": 7234, "crc": -1131827529}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$2.class", "size": 3041, "crc": 793345953}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "size": 2066, "crc": -364222940}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "size": 2060, "crc": 1133478653}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "size": 6614, "crc": -120143721}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$1$1.class", "size": 2121, "crc": 634233376}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$2.class", "size": 1973, "crc": 2106934384}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1$1.class", "size": 1698, "crc": -841961959}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "size": 4233, "crc": -183162186}, {"key": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$1.class", "size": 1783, "crc": -689586448}, {"key": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$3$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$3$1.class", "size": 2995, "crc": 1266830303}, {"key": "androidx/compose/material/BackdropScaffoldKt.class", "name": "androidx/compose/material/BackdropScaffoldKt.class", "size": 46238, "crc": 306390013}, {"key": "androidx/compose/material/BackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldState$1.class", "size": 1769, "crc": -1398334479}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$1.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$1.class", "size": 2444, "crc": 1611900076}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$2.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$2.class", "size": 3208, "crc": -305896567}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion.class", "size": 2921, "crc": 387286474}, {"key": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$1.class", "size": 2118, "crc": 24291592}, {"key": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$2.class", "size": 1985, "crc": -1315517234}, {"key": "androidx/compose/material/BackdropScaffoldState.class", "name": "androidx/compose/material/BackdropScaffoldState.class", "size": 9831, "crc": 1007582964}, {"key": "androidx/compose/material/BackdropValue.class", "name": "androidx/compose/material/BackdropValue.class", "size": 1402, "crc": 1562813470}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "size": 2993, "crc": 1563263536}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1.class", "size": 4706, "crc": -993777073}, {"key": "androidx/compose/material/BadgeKt$Badge$2.class", "name": "androidx/compose/material/BadgeKt$Badge$2.class", "size": 2337, "crc": -124227287}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "size": 2672, "crc": 213582471}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$2.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$2.class", "size": 5776, "crc": -213178679}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$3.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$3.class", "size": 2487, "crc": -941870490}, {"key": "androidx/compose/material/BadgeKt.class", "name": "androidx/compose/material/BadgeKt.class", "size": 21925, "crc": -235210151}, {"key": "androidx/compose/material/BottomAppBarCutoutShape.class", "name": "androidx/compose/material/BottomAppBarCutoutShape.class", "size": 9325, "crc": 2060292633}, {"key": "androidx/compose/material/BottomDrawerState$1.class", "name": "androidx/compose/material/BottomDrawerState$1.class", "size": 1756, "crc": 1779785848}, {"key": "androidx/compose/material/BottomDrawerState$Companion$Saver$1.class", "name": "androidx/compose/material/BottomDrawerState$Companion$Saver$1.class", "size": 2377, "crc": -1534762028}, {"key": "androidx/compose/material/BottomDrawerState$Companion$Saver$2.class", "name": "androidx/compose/material/BottomDrawerState$Companion$Saver$2.class", "size": 2773, "crc": -1920277704}, {"key": "androidx/compose/material/BottomDrawerState$Companion.class", "name": "androidx/compose/material/BottomDrawerState$Companion.class", "size": 2691, "crc": 997373054}, {"key": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$1.class", "size": 2350, "crc": -129973119}, {"key": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$2.class", "size": 2217, "crc": -1657167628}, {"key": "androidx/compose/material/BottomDrawerState.class", "name": "androidx/compose/material/BottomDrawerState.class", "size": 10600, "crc": -565323584}, {"key": "androidx/compose/material/BottomDrawerValue.class", "name": "androidx/compose/material/BottomDrawerValue.class", "size": 1471, "crc": 812788055}, {"key": "androidx/compose/material/BottomNavigationDefaults.class", "name": "androidx/compose/material/BottomNavigationDefaults.class", "size": 4019, "crc": -633193403}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "size": 10660, "crc": -248223591}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$2.class", "size": 2724, "crc": 361788910}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$3.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$3.class", "size": 2472, "crc": -1208505686}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "size": 3643, "crc": -1746220003}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$2.class", "size": 3635, "crc": 1115893790}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "size": 4586, "crc": 996853033}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "size": 6205, "crc": 881208556}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$3.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$3.class", "size": 2367, "crc": -765343287}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "size": 3250, "crc": -1805296950}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$2.class", "size": 2265, "crc": -1603416509}, {"key": "androidx/compose/material/BottomNavigationKt$placeIcon$1.class", "name": "androidx/compose/material/BottomNavigationKt$placeIcon$1.class", "size": 1999, "crc": -1018463898}, {"key": "androidx/compose/material/BottomNavigationKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material/BottomNavigationKt$placeLabelAndIcon$1.class", "size": 2495, "crc": -1493856351}, {"key": "androidx/compose/material/BottomNavigationKt.class", "name": "androidx/compose/material/BottomNavigationKt.class", "size": 37488, "crc": 758347654}, {"key": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "name": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "size": 3066, "crc": -1328352995}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$WhenMappings.class", "size": 883, "crc": 163089635}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$newAnchors$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$newAnchors$1.class", "size": 2148, "crc": 1348622243}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1.class", "size": 3843, "crc": 1959891324}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "size": 3639, "crc": 895936130}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1.class", "size": 2595, "crc": 1894974614}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "size": 3641, "crc": 557570649}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2.class", "size": 2596, "crc": 1090584152}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1.class", "size": 2901, "crc": 2048954706}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "size": 9605, "crc": -239932882}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$4.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$4.class", "size": 3077, "crc": 733384090}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "size": 3169, "crc": 883041357}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "size": 7463, "crc": 57264570}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "size": 3282, "crc": -466252954}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$4$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$4$1.class", "size": 1645, "crc": 288582146}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "size": 8734, "crc": 1116561843}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "size": 4811, "crc": -795498039}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1$WhenMappings.class", "size": 1006, "crc": -1462341253}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1.class", "size": 7757, "crc": 1144807945}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "size": 12315, "crc": 1500101286}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2.class", "size": 3609, "crc": 1798030890}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2277, "crc": 1711958926}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2271, "crc": -1620241669}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6773, "crc": -143495164}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$1.class", "size": 1745, "crc": -1160410883}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$2$1.class", "size": 2523, "crc": 1697498774}, {"key": "androidx/compose/material/BottomSheetScaffoldKt.class", "name": "androidx/compose/material/BottomSheetScaffoldKt.class", "size": 38046, "crc": 63508252}, {"key": "androidx/compose/material/BottomSheetScaffoldState.class", "name": "androidx/compose/material/BottomSheetScaffoldState.class", "size": 1508, "crc": -1722098199}, {"key": "androidx/compose/material/BottomSheetState$1.class", "name": "androidx/compose/material/BottomSheetState$1.class", "size": 1761, "crc": -1985773981}, {"key": "androidx/compose/material/BottomSheetState$Companion$Saver$1.class", "name": "androidx/compose/material/BottomSheetState$Companion$Saver$1.class", "size": 2379, "crc": -506089221}, {"key": "androidx/compose/material/BottomSheetState$Companion$Saver$2.class", "name": "androidx/compose/material/BottomSheetState$Companion$Saver$2.class", "size": 2792, "crc": -1889536429}, {"key": "androidx/compose/material/BottomSheetState$Companion.class", "name": "androidx/compose/material/BottomSheetState$Companion.class", "size": 2648, "crc": 223057908}, {"key": "androidx/compose/material/BottomSheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BottomSheetState$anchoredDraggableState$1.class", "size": 2434, "crc": -178712312}, {"key": "androidx/compose/material/BottomSheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BottomSheetState$anchoredDraggableState$2.class", "size": 2301, "crc": 97939079}, {"key": "androidx/compose/material/BottomSheetState.class", "name": "androidx/compose/material/BottomSheetState.class", "size": 8594, "crc": 1436046529}, {"key": "androidx/compose/material/BottomSheetValue.class", "name": "androidx/compose/material/BottomSheetValue.class", "size": 1423, "crc": -888490641}, {"key": "androidx/compose/material/ButtonColors.class", "name": "androidx/compose/material/ButtonColors.class", "size": 1135, "crc": 635065857}, {"key": "androidx/compose/material/ButtonDefaults.class", "name": "androidx/compose/material/ButtonDefaults.class", "size": 13125, "crc": -57921101}, {"key": "androidx/compose/material/ButtonElevation.class", "name": "androidx/compose/material/ButtonElevation.class", "size": 1251, "crc": 1215185822}, {"key": "androidx/compose/material/ButtonKt$Button$1.class", "name": "androidx/compose/material/ButtonKt$Button$1.class", "size": 2273, "crc": 953105915}, {"key": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "size": 10142, "crc": 1437929344}, {"key": "androidx/compose/material/ButtonKt$Button$2$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1.class", "size": 3646, "crc": 1822828701}, {"key": "androidx/compose/material/ButtonKt$Button$2.class", "name": "androidx/compose/material/ButtonKt$Button$2.class", "size": 4659, "crc": 39676765}, {"key": "androidx/compose/material/ButtonKt$Button$3.class", "name": "androidx/compose/material/ButtonKt$Button$3.class", "size": 3958, "crc": 132495504}, {"key": "androidx/compose/material/ButtonKt.class", "name": "androidx/compose/material/ButtonKt.class", "size": 14775, "crc": 1673361108}, {"key": "androidx/compose/material/CardKt.class", "name": "androidx/compose/material/CardKt.class", "size": 6408, "crc": 174355040}, {"key": "androidx/compose/material/CheckDrawingCache.class", "name": "androidx/compose/material/CheckDrawingCache.class", "size": 2131, "crc": -1304277286}, {"key": "androidx/compose/material/CheckboxColors.class", "name": "androidx/compose/material/CheckboxColors.class", "size": 1690, "crc": -1803122616}, {"key": "androidx/compose/material/CheckboxDefaults.class", "name": "androidx/compose/material/CheckboxDefaults.class", "size": 5202, "crc": -816552434}, {"key": "androidx/compose/material/CheckboxKt$Checkbox$1$1.class", "name": "androidx/compose/material/CheckboxKt$Checkbox$1$1.class", "size": 1778, "crc": -2094579183}, {"key": "androidx/compose/material/CheckboxKt$Checkbox$2.class", "name": "androidx/compose/material/CheckboxKt$Checkbox$2.class", "size": 2727, "crc": -509852315}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$1$1.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$1$1.class", "size": 3597, "crc": -2117125922}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$2.class", "size": 2126, "crc": 1037819620}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "size": 3827, "crc": -327060934}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "size": 3817, "crc": 1631687313}, {"key": "androidx/compose/material/CheckboxKt$TriStateCheckbox$1.class", "name": "androidx/compose/material/CheckboxKt$TriStateCheckbox$1.class", "size": 2874, "crc": 2141181557}, {"key": "androidx/compose/material/CheckboxKt$WhenMappings.class", "name": "androidx/compose/material/CheckboxKt$WhenMappings.class", "size": 848, "crc": 1073703979}, {"key": "androidx/compose/material/CheckboxKt.class", "name": "androidx/compose/material/CheckboxKt.class", "size": 28017, "crc": 1298413061}, {"key": "androidx/compose/material/ChipColors.class", "name": "androidx/compose/material/ChipColors.class", "size": 1295, "crc": -66202047}, {"key": "androidx/compose/material/ChipDefaults.class", "name": "androidx/compose/material/ChipDefaults.class", "size": 10499, "crc": -220863654}, {"key": "androidx/compose/material/ChipKt$Chip$1.class", "name": "androidx/compose/material/ChipKt$Chip$1.class", "size": 2196, "crc": 345504917}, {"key": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "size": 13380, "crc": -749156728}, {"key": "androidx/compose/material/ChipKt$Chip$2$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1.class", "size": 3970, "crc": -1536220783}, {"key": "androidx/compose/material/ChipKt$Chip$2.class", "name": "androidx/compose/material/ChipKt$Chip$2.class", "size": 4939, "crc": -315733036}, {"key": "androidx/compose/material/ChipKt$Chip$3.class", "name": "androidx/compose/material/ChipKt$Chip$3.class", "size": 3818, "crc": 1373176513}, {"key": "androidx/compose/material/ChipKt$FilterChip$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$1.class", "size": 2292, "crc": 26159360}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "size": 22275, "crc": 1536534907}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "size": 4748, "crc": 1579843780}, {"key": "androidx/compose/material/ChipKt$FilterChip$2.class", "name": "androidx/compose/material/ChipKt$FilterChip$2.class", "size": 5669, "crc": 1421843000}, {"key": "androidx/compose/material/ChipKt$FilterChip$3.class", "name": "androidx/compose/material/ChipKt$FilterChip$3.class", "size": 4444, "crc": 1305061800}, {"key": "androidx/compose/material/ChipKt.class", "name": "androidx/compose/material/ChipKt.class", "size": 17273, "crc": 1390769897}, {"key": "androidx/compose/material/Colors.class", "name": "androidx/compose/material/Colors.class", "size": 12467, "crc": -102170312}, {"key": "androidx/compose/material/ColorsKt$LocalColors$1.class", "name": "androidx/compose/material/ColorsKt$LocalColors$1.class", "size": 1319, "crc": 1845111288}, {"key": "androidx/compose/material/ColorsKt.class", "name": "androidx/compose/material/ColorsKt.class", "size": 9268, "crc": -1239904113}, {"key": "androidx/compose/material/CompatRippleTheme.class", "name": "androidx/compose/material/CompatRippleTheme.class", "size": 4631, "crc": 140922461}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-1$1.class", "size": 2478, "crc": 995497163}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-2$1.class", "size": 2479, "crc": 1059099285}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "size": 1901, "crc": 308571300}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda-1$1.class", "size": 3033, "crc": -294064225}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "size": 1618, "crc": -1372009196}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "size": 3054, "crc": -370163705}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "size": 2266, "crc": -106950558}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "size": 2266, "crc": 2123300885}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "size": 2669, "crc": -1301682170}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "size": 2189, "crc": -1921362819}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "size": 2189, "crc": -812391867}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "size": 2976, "crc": -1655078199}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "size": 2189, "crc": -1467323693}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-5$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-5$1.class", "size": 2189, "crc": 1067707937}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-6$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-6$1.class", "size": 2189, "crc": 1822020693}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-7$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-7$1.class", "size": 2977, "crc": -101281473}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-8$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-8$1.class", "size": 2189, "crc": -1143554339}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "size": 4255, "crc": 892842140}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "size": 3061, "crc": 1522759339}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "size": 1588, "crc": -852760113}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-1$1.class", "size": 2508, "crc": 328487192}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-2$1.class", "size": 2509, "crc": -1981421446}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "size": 1811, "crc": 61471005}, {"key": "androidx/compose/material/ContentAlpha.class", "name": "androidx/compose/material/ContentAlpha.class", "size": 4900, "crc": 1317481513}, {"key": "androidx/compose/material/ContentAlphaKt$LocalContentAlpha$1.class", "name": "androidx/compose/material/ContentAlphaKt$LocalContentAlpha$1.class", "size": 1232, "crc": -442593353}, {"key": "androidx/compose/material/ContentAlphaKt.class", "name": "androidx/compose/material/ContentAlphaKt.class", "size": 1420, "crc": -319738014}, {"key": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "name": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "size": 1410, "crc": 195926252}, {"key": "androidx/compose/material/ContentColorKt.class", "name": "androidx/compose/material/ContentColorKt.class", "size": 1494, "crc": -994492592}, {"key": "androidx/compose/material/DefaultButtonColors.class", "name": "androidx/compose/material/DefaultButtonColors.class", "size": 3991, "crc": -1193194536}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "size": 3688, "crc": 2048040642}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "size": 4439, "crc": 527961329}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "size": 6168, "crc": 1274209142}, {"key": "androidx/compose/material/DefaultButtonElevation.class", "name": "androidx/compose/material/DefaultButtonElevation.class", "size": 9329, "crc": -1654429384}, {"key": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "name": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "size": 870, "crc": -1998232282}, {"key": "androidx/compose/material/DefaultCheckboxColors.class", "name": "androidx/compose/material/DefaultCheckboxColors.class", "size": 6790, "crc": 994630538}, {"key": "androidx/compose/material/DefaultChipColors.class", "name": "androidx/compose/material/DefaultChipColors.class", "size": 4760, "crc": -2113576239}, {"key": "androidx/compose/material/DefaultElevationOverlay.class", "name": "androidx/compose/material/DefaultElevationOverlay.class", "size": 3623, "crc": 1387810574}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "size": 4361, "crc": 1845308974}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "size": 4085, "crc": -1925689795}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "size": 4707, "crc": 870154306}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "size": 4676, "crc": -1857428974}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "size": 8070, "crc": -1170411915}, {"key": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "name": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "size": 1116, "crc": 1353844276}, {"key": "androidx/compose/material/DefaultRadioButtonColors.class", "name": "androidx/compose/material/DefaultRadioButtonColors.class", "size": 4266, "crc": -572943783}, {"key": "androidx/compose/material/DefaultSelectableChipColors.class", "name": "androidx/compose/material/DefaultSelectableChipColors.class", "size": 5504, "crc": -826622384}, {"key": "androidx/compose/material/DefaultSliderColors.class", "name": "androidx/compose/material/DefaultSliderColors.class", "size": 5656, "crc": -337172919}, {"key": "androidx/compose/material/DefaultSwitchColors.class", "name": "androidx/compose/material/DefaultSwitchColors.class", "size": 4798, "crc": -1900683617}, {"key": "androidx/compose/material/DefaultTextFieldColors.class", "name": "androidx/compose/material/DefaultTextFieldColors.class", "size": 13783, "crc": -1849546521}, {"key": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "name": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "size": 14328, "crc": 1838195097}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "size": 3621, "crc": -1133687243}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "size": 2785, "crc": -828042918}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "size": 2183, "crc": -1408256601}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "size": 5170, "crc": -97035377}, {"key": "androidx/compose/material/DismissDirection.class", "name": "androidx/compose/material/DismissDirection.class", "size": 1421, "crc": -1285264132}, {"key": "androidx/compose/material/DismissState$1.class", "name": "androidx/compose/material/DismissState$1.class", "size": 1643, "crc": -931075244}, {"key": "androidx/compose/material/DismissState$Companion$Saver$1.class", "name": "androidx/compose/material/DismissState$Companion$Saver$1.class", "size": 2082, "crc": -196511847}, {"key": "androidx/compose/material/DismissState$Companion$Saver$2.class", "name": "androidx/compose/material/DismissState$Companion$Saver$2.class", "size": 2102, "crc": -323331046}, {"key": "androidx/compose/material/DismissState$Companion.class", "name": "androidx/compose/material/DismissState$Companion.class", "size": 2180, "crc": 262355739}, {"key": "androidx/compose/material/DismissState.class", "name": "androidx/compose/material/DismissState.class", "size": 4554, "crc": 89784021}, {"key": "androidx/compose/material/DismissValue.class", "name": "androidx/compose/material/DismissValue.class", "size": 1468, "crc": -1501005218}, {"key": "androidx/compose/material/DividerKt$Divider$1.class", "name": "androidx/compose/material/DividerKt$Divider$1.class", "size": 1895, "crc": 1648488735}, {"key": "androidx/compose/material/DividerKt.class", "name": "androidx/compose/material/DividerKt.class", "size": 6676, "crc": -10263510}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2087, "crc": -151784382}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt.class", "size": 15762, "crc": -674848661}, {"key": "androidx/compose/material/DraggableAnchors.class", "name": "androidx/compose/material/DraggableAnchors.class", "size": 1232, "crc": -1043829256}, {"key": "androidx/compose/material/DraggableAnchorsConfig.class", "name": "androidx/compose/material/DraggableAnchorsConfig.class", "size": 1724, "crc": -816868204}, {"key": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "size": 3349, "crc": -777481772}, {"key": "androidx/compose/material/DraggableAnchorsElement.class", "name": "androidx/compose/material/DraggableAnchorsElement.class", "size": 6544, "crc": -106707571}, {"key": "androidx/compose/material/DraggableAnchorsNode$measure$1.class", "name": "androidx/compose/material/DraggableAnchorsNode$measure$1.class", "size": 3420, "crc": -1899004819}, {"key": "androidx/compose/material/DraggableAnchorsNode.class", "name": "androidx/compose/material/DraggableAnchorsNode.class", "size": 6476, "crc": 107864018}, {"key": "androidx/compose/material/DrawerDefaults.class", "name": "androidx/compose/material/DrawerDefaults.class", "size": 5047, "crc": 2219935}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "size": 3553, "crc": 1774586223}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1.class", "size": 2245, "crc": 180664829}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$WhenMappings.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$WhenMappings.class", "size": 961, "crc": 2118419704}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$newAnchors$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$newAnchors$1.class", "size": 2276, "crc": 1137201004}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1.class", "size": 3435, "crc": 978858783}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$3$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$3$1.class", "size": 1989, "crc": 317921149}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "size": 3650, "crc": -17023595}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1.class", "size": 2338, "crc": 64095029}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1.class", "size": 2584, "crc": 2114635667}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "size": 9375, "crc": 518908098}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "size": 19825, "crc": 1100529849}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$2.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$2.class", "size": 3367, "crc": 523439065}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$1$1.class", "size": 2129, "crc": 205632867}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$2.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$2.class", "size": 1981, "crc": 1288251404}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1$1.class", "size": 1694, "crc": -1942534850}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "size": 4235, "crc": -889364301}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1$1.class", "size": 1535, "crc": 596149412}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1.class", "size": 2449, "crc": -1644908320}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2160, "crc": 1905991849}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2154, "crc": -455984161}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6729, "crc": 2029250421}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1$anchors$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1$anchors$1.class", "size": 1966, "crc": 744617647}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1.class", "size": 2345, "crc": 1703664051}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "size": 3523, "crc": 987534736}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1.class", "size": 2517, "crc": 1574571538}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$3$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$3$1.class", "size": 1670, "crc": 1619619519}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$5$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$5$1.class", "size": 1964, "crc": 151115951}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "size": 3619, "crc": -980156688}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1.class", "size": 2590, "crc": 2078379987}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1.class", "size": 2556, "crc": 78875085}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "size": 9586, "crc": 1848450476}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "size": 22185, "crc": 1098179724}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$2.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$2.class", "size": 3340, "crc": 1262860736}, {"key": "androidx/compose/material/DrawerKt$Scrim$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$1$1.class", "size": 2155, "crc": 610815597}, {"key": "androidx/compose/material/DrawerKt$Scrim$2.class", "name": "androidx/compose/material/DrawerKt$Scrim$2.class", "size": 2154, "crc": 555350185}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1$1.class", "size": 1650, "crc": 903990032}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "size": 4183, "crc": -2092503561}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1$1.class", "size": 1491, "crc": 808173195}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1.class", "size": 2425, "crc": -1953736163}, {"key": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$1.class", "name": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$1.class", "size": 1702, "crc": -871393289}, {"key": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$2$1.class", "name": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$2$1.class", "size": 2465, "crc": 1755636363}, {"key": "androidx/compose/material/DrawerKt$rememberDrawerState$1.class", "name": "androidx/compose/material/DrawerKt$rememberDrawerState$1.class", "size": 1601, "crc": 278625938}, {"key": "androidx/compose/material/DrawerKt$rememberDrawerState$2$1.class", "name": "androidx/compose/material/DrawerKt$rememberDrawerState$2$1.class", "size": 1886, "crc": -1766016367}, {"key": "androidx/compose/material/DrawerKt.class", "name": "androidx/compose/material/DrawerKt.class", "size": 34296, "crc": -1212640855}, {"key": "androidx/compose/material/DrawerState$1.class", "name": "androidx/compose/material/DrawerState$1.class", "size": 1627, "crc": -173498510}, {"key": "androidx/compose/material/DrawerState$Companion$Saver$1.class", "name": "androidx/compose/material/DrawerState$Companion$Saver$1.class", "size": 2038, "crc": -1849231962}, {"key": "androidx/compose/material/DrawerState$Companion$Saver$2.class", "name": "androidx/compose/material/DrawerState$Companion$Saver$2.class", "size": 2080, "crc": -1888317122}, {"key": "androidx/compose/material/DrawerState$Companion.class", "name": "androidx/compose/material/DrawerState$Companion.class", "size": 2161, "crc": 1910097656}, {"key": "androidx/compose/material/DrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material/DrawerState$anchoredDraggableState$1.class", "size": 2399, "crc": -494141820}, {"key": "androidx/compose/material/DrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material/DrawerState$anchoredDraggableState$2.class", "size": 2266, "crc": -**********}, {"key": "androidx/compose/material/DrawerState.class", "name": "androidx/compose/material/DrawerState.class", "size": 8046, "crc": 939876562}, {"key": "androidx/compose/material/DrawerValue.class", "name": "androidx/compose/material/DrawerValue.class", "size": 1373, "crc": 4826579}, {"key": "androidx/compose/material/DropdownMenuPositionProvider$1.class", "name": "androidx/compose/material/DropdownMenuPositionProvider$1.class", "size": 1714, "crc": -122101449}, {"key": "androidx/compose/material/DropdownMenuPositionProvider.class", "name": "androidx/compose/material/DropdownMenuPositionProvider.class", "size": 10261, "crc": -**********}, {"key": "androidx/compose/material/ElevationDefaults.class", "name": "androidx/compose/material/ElevationDefaults.class", "size": 2385, "crc": -**********}, {"key": "androidx/compose/material/ElevationKt.class", "name": "androidx/compose/material/ElevationKt.class", "size": 4555, "crc": -**********}, {"key": "androidx/compose/material/ElevationOverlay.class", "name": "androidx/compose/material/ElevationOverlay.class", "size": 835, "crc": 152710662}, {"key": "androidx/compose/material/ElevationOverlayKt$LocalAbsoluteElevation$1.class", "name": "androidx/compose/material/ElevationOverlayKt$LocalAbsoluteElevation$1.class", "size": 2143, "crc": 312288924}, {"key": "androidx/compose/material/ElevationOverlayKt$LocalElevationOverlay$1.class", "name": "androidx/compose/material/ElevationOverlayKt$LocalElevationOverlay$1.class", "size": 1456, "crc": -136386148}, {"key": "androidx/compose/material/ElevationOverlayKt.class", "name": "androidx/compose/material/ElevationOverlayKt.class", "size": 3950, "crc": 1855970401}, {"key": "androidx/compose/material/ExperimentalMaterialApi.class", "name": "androidx/compose/material/ExperimentalMaterialApi.class", "size": 822, "crc": 1257411371}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "size": 4784, "crc": 28593805}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "size": 3095, "crc": **********}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "size": 2541, "crc": 738740113}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "size": 11991, "crc": **********}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "size": 1158, "crc": -**********}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "size": 1677, "crc": -**********}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "size": 3525, "crc": **********}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$4.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$4.class", "size": 2216, "crc": 1189561780}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "size": 13818, "crc": -1165702033}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1$1.class", "size": 1684, "crc": 983972155}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "size": 3198, "crc": 2094367714}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "size": 1821, "crc": 1766121247}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "size": 1572, "crc": 1018368730}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$invoke$$inlined$onDispose$1.class", "size": 2307, "crc": -1789579230}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$listener$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$listener$1$1.class", "size": 1767, "crc": -436742903}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$listener$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$listener$1.class", "size": 2536, "crc": -1332700114}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "size": 4056, "crc": -335052778}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "size": 2724, "crc": 1426171359}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "size": 2828, "crc": -106051918}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "size": 4685, "crc": 202916228}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "size": 4076, "crc": -1268100661}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2$1.class", "size": 1555, "crc": 1586790992}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2.class", "size": 2508, "crc": -259526217}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "size": 24126, "crc": -177520688}, {"key": "androidx/compose/material/FabPlacement.class", "name": "androidx/compose/material/FabPlacement.class", "size": 1317, "crc": 147656333}, {"key": "androidx/compose/material/FabPosition$Companion.class", "name": "androidx/compose/material/FabPosition$Companion.class", "size": 1364, "crc": -2014801931}, {"key": "androidx/compose/material/FabPosition.class", "name": "androidx/compose/material/FabPosition.class", "size": 2702, "crc": -592138911}, {"key": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "name": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "size": 4391, "crc": 1364892100}, {"key": "androidx/compose/material/FadeInFadeOutState.class", "name": "androidx/compose/material/FadeInFadeOutState.class", "size": 2302, "crc": -846782900}, {"key": "androidx/compose/material/FixedThreshold.class", "name": "androidx/compose/material/FixedThreshold.class", "size": 3364, "crc": 120526700}, {"key": "androidx/compose/material/FloatingActionButtonDefaults.class", "name": "androidx/compose/material/FloatingActionButtonDefaults.class", "size": 5963, "crc": 1335020025}, {"key": "androidx/compose/material/FloatingActionButtonElevation.class", "name": "androidx/compose/material/FloatingActionButtonElevation.class", "size": 1258, "crc": 1492312872}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "size": 2008, "crc": -992348652}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "size": 2064, "crc": 834064409}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "size": 7122, "crc": -1890432447}, {"key": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "size": 11107, "crc": 1422147094}, {"key": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "name": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "size": 3647, "crc": 1895537266}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$1.class", "size": 2262, "crc": -1792151433}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "size": 9422, "crc": -234707404}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "size": 3503, "crc": 521586318}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "size": 4070, "crc": -650337545}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$3.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$3.class", "size": 3405, "crc": -81173396}, {"key": "androidx/compose/material/FloatingActionButtonKt.class", "name": "androidx/compose/material/FloatingActionButtonKt.class", "size": 15375, "crc": -1588154304}, {"key": "androidx/compose/material/FractionalThreshold.class", "name": "androidx/compose/material/FractionalThreshold.class", "size": 2887, "crc": -1908449116}, {"key": "androidx/compose/material/HighContrastContentAlpha.class", "name": "androidx/compose/material/HighContrastContentAlpha.class", "size": 903, "crc": 953187818}, {"key": "androidx/compose/material/IconButtonKt$IconButton$2.class", "name": "androidx/compose/material/IconButtonKt$IconButton$2.class", "size": 2786, "crc": -850307147}, {"key": "androidx/compose/material/IconButtonKt$IconToggleButton$2.class", "name": "androidx/compose/material/IconButtonKt$IconToggleButton$2.class", "size": 2903, "crc": 1225570982}, {"key": "androidx/compose/material/IconButtonKt.class", "name": "androidx/compose/material/IconButtonKt.class", "size": 17692, "crc": -1513337530}, {"key": "androidx/compose/material/IconKt$Icon$1.class", "name": "androidx/compose/material/IconKt$Icon$1.class", "size": 2067, "crc": -1428312834}, {"key": "androidx/compose/material/IconKt$Icon$semantics$1$1.class", "name": "androidx/compose/material/IconKt$Icon$semantics$1$1.class", "size": 2162, "crc": -1786105525}, {"key": "androidx/compose/material/IconKt.class", "name": "androidx/compose/material/IconKt.class", "size": 13013, "crc": -1426149270}, {"key": "androidx/compose/material/InputPhase.class", "name": "androidx/compose/material/InputPhase.class", "size": 1456, "crc": -1872102879}, {"key": "androidx/compose/material/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "name": "androidx/compose/material/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "size": 1340, "crc": 904367573}, {"key": "androidx/compose/material/InteractiveComponentSizeKt.class", "name": "androidx/compose/material/InteractiveComponentSizeKt.class", "size": 4005, "crc": 1825399127}, {"key": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "name": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "size": 2052, "crc": 1037235500}, {"key": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "size": 7342, "crc": 862734998}, {"key": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "size": 7518, "crc": -1040947709}, {"key": "androidx/compose/material/InternalMutatorMutex.class", "name": "androidx/compose/material/InternalMutatorMutex.class", "size": 6737, "crc": -1913470724}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1$2.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1$2.class", "size": 3474, "crc": -1149124438}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "size": 6722, "crc": 60388065}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$2.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$2.class", "size": 2417, "crc": 1023371612}, {"key": "androidx/compose/material/ListItemKt$ListItem$1.class", "name": "androidx/compose/material/ListItemKt$ListItem$1.class", "size": 3111, "crc": -1479230331}, {"key": "androidx/compose/material/ListItemKt$ListItem$semanticsModifier$1.class", "name": "androidx/compose/material/ListItemKt$ListItem$semanticsModifier$1.class", "size": 1778, "crc": -574654370}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1$1.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1$1.class", "size": 2010, "crc": -248908420}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "size": 3962, "crc": 610909695}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$2.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$2.class", "size": 2295, "crc": 70506455}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "size": 3841, "crc": -501756586}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "size": 4029, "crc": -1454390019}, {"key": "androidx/compose/material/ListItemKt.class", "name": "androidx/compose/material/ListItemKt.class", "size": 18814, "crc": -291465111}, {"key": "androidx/compose/material/LowContrastContentAlpha.class", "name": "androidx/compose/material/LowContrastContentAlpha.class", "size": 901, "crc": 1254681828}, {"key": "androidx/compose/material/MapDraggableAnchors.class", "name": "androidx/compose/material/MapDraggableAnchors.class", "size": 5407, "crc": 305250713}, {"key": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "name": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "size": 7959, "crc": 106650814}, {"key": "androidx/compose/material/MaterialTheme.class", "name": "androidx/compose/material/MaterialTheme.class", "size": 4585, "crc": 980922909}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "size": 2634, "crc": 2118060609}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "size": 3454, "crc": -2128281134}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$2.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$2.class", "size": 2580, "crc": -2113097335}, {"key": "androidx/compose/material/MaterialThemeKt.class", "name": "androidx/compose/material/MaterialThemeKt.class", "size": 8822, "crc": -36791082}, {"key": "androidx/compose/material/MaterialTheme_androidKt$PlatformMaterialTheme$1.class", "name": "androidx/compose/material/MaterialTheme_androidKt$PlatformMaterialTheme$1.class", "size": 2003, "crc": -1893382472}, {"key": "androidx/compose/material/MaterialTheme_androidKt.class", "name": "androidx/compose/material/MaterialTheme_androidKt.class", "size": 2614, "crc": 861467355}, {"key": "androidx/compose/material/MenuDefaults.class", "name": "androidx/compose/material/MenuDefaults.class", "size": 2086, "crc": -62133810}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$1$1.class", "size": 2870, "crc": 181258525}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "size": 10534, "crc": 302174962}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$3.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$3.class", "size": 3230, "crc": -1033978681}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "size": 3586, "crc": -130541859}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "size": 3734, "crc": -1876353429}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "size": 3063, "crc": 47572752}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "size": 4602, "crc": -161685069}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$2.class", "size": 3151, "crc": -2025473932}, {"key": "androidx/compose/material/MenuKt.class", "name": "androidx/compose/material/MenuKt.class", "size": 26887, "crc": -1446584910}, {"key": "androidx/compose/material/MinimumInteractiveComponentSizeModifier$measure$1.class", "name": "androidx/compose/material/MinimumInteractiveComponentSizeModifier$measure$1.class", "size": 2283, "crc": -418052596}, {"key": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "name": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "size": 3363, "crc": 297508016}, {"key": "androidx/compose/material/MinimumInteractiveModifier.class", "name": "androidx/compose/material/MinimumInteractiveModifier.class", "size": 3145, "crc": -929837737}, {"key": "androidx/compose/material/MinimumInteractiveModifierNode$measure$1.class", "name": "androidx/compose/material/MinimumInteractiveModifierNode$measure$1.class", "size": 2256, "crc": -70284349}, {"key": "androidx/compose/material/MinimumInteractiveModifierNode.class", "name": "androidx/compose/material/MinimumInteractiveModifierNode.class", "size": 3578, "crc": -2087603547}, {"key": "androidx/compose/material/ModalBottomSheetDefaults.class", "name": "androidx/compose/material/ModalBottomSheetDefaults.class", "size": 4210, "crc": -1940865849}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2250, "crc": -335282940}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2244, "crc": -1976448337}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6752, "crc": 206221458}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "size": 3614, "crc": 1363344469}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1.class", "size": 2723, "crc": -344710194}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "size": 3719, "crc": -1623750544}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1.class", "size": 2669, "crc": 866510687}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "size": 3738, "crc": 1132178178}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2.class", "size": 2675, "crc": 1396734278}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "size": 3742, "crc": -1395353718}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3.class", "size": 2679, "crc": -853760001}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1.class", "size": 3257, "crc": -1749656534}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "size": 9715, "crc": 243428582}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$2.class", "size": 3452, "crc": 380145497}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$1$1.class", "size": 2121, "crc": -1597628322}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$2.class", "size": 1973, "crc": 1381775551}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1$1.class", "size": 1698, "crc": 487842665}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "size": 4233, "crc": 9197863}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1$1.class", "size": 1539, "crc": 1303892417}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1.class", "size": 2450, "crc": 1319301824}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$WhenMappings.class", "size": 958, "crc": -602339800}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$newAnchors$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$newAnchors$1.class", "size": 2585, "crc": -1215599704}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1.class", "size": 4187, "crc": -1870286955}, {"key": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "size": 1779, "crc": 1172053821}, {"key": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$2$1.class", "size": 2650, "crc": 1438197815}, {"key": "androidx/compose/material/ModalBottomSheetKt.class", "name": "androidx/compose/material/ModalBottomSheetKt.class", "size": 37905, "crc": 87296743}, {"key": "androidx/compose/material/ModalBottomSheetState$1.class", "name": "androidx/compose/material/ModalBottomSheetState$1.class", "size": 1799, "crc": 1551852825}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$1.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$1.class", "size": 2240, "crc": -2039392707}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$2.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$2.class", "size": 2929, "crc": -1628744313}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion.class", "size": 2747, "crc": 1292153735}, {"key": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "size": 804, "crc": -1753431140}, {"key": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$1.class", "size": 1930, "crc": 30921050}, {"key": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$2.class", "size": 2316, "crc": -1469585716}, {"key": "androidx/compose/material/ModalBottomSheetState.class", "name": "androidx/compose/material/ModalBottomSheetState.class", "size": 10446, "crc": 2109945569}, {"key": "androidx/compose/material/ModalBottomSheetValue.class", "name": "androidx/compose/material/ModalBottomSheetValue.class", "size": 1513, "crc": -1374725986}, {"key": "androidx/compose/material/MutableWindowInsets.class", "name": "androidx/compose/material/MutableWindowInsets.class", "size": 4138, "crc": -1746900358}, {"key": "androidx/compose/material/NavigationRailDefaults.class", "name": "androidx/compose/material/NavigationRailDefaults.class", "size": 3984, "crc": -964990943}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "size": 11718, "crc": -1385012070}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$2.class", "size": 2988, "crc": 583775304}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$3.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$3.class", "size": 2736, "crc": 1036296144}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "size": 3575, "crc": -1593959483}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$2.class", "size": 3302, "crc": 858734161}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "size": 4520, "crc": 611872083}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "size": 6151, "crc": -1991927182}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$3.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$3.class", "size": 2351, "crc": 767540326}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "size": 3228, "crc": 179226574}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$2.class", "size": 2249, "crc": -1702590281}, {"key": "androidx/compose/material/NavigationRailKt$placeIcon$1.class", "name": "androidx/compose/material/NavigationRailKt$placeIcon$1.class", "size": 2037, "crc": -2003597146}, {"key": "androidx/compose/material/NavigationRailKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material/NavigationRailKt$placeLabelAndIcon$1.class", "size": 2487, "crc": 1030942887}, {"key": "androidx/compose/material/NavigationRailKt.class", "name": "androidx/compose/material/NavigationRailKt.class", "size": 37696, "crc": -13062489}, {"key": "androidx/compose/material/OnGlobalLayoutListener.class", "name": "androidx/compose/material/OnGlobalLayoutListener.class", "size": 3011, "crc": 906646687}, {"key": "androidx/compose/material/OneLine$ListItem$2.class", "name": "androidx/compose/material/OneLine$ListItem$2.class", "size": 2730, "crc": -122555681}, {"key": "androidx/compose/material/OneLine.class", "name": "androidx/compose/material/OneLine.class", "size": 19438, "crc": -676626593}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$1.class", "size": 2163, "crc": -860636787}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$10.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$10.class", "size": 5594, "crc": 41929286}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$12.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$12.class", "size": 5541, "crc": -109031081}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$1.class", "size": 3333, "crc": 1617936505}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "size": 6852, "crc": 271033843}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$4.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$4.class", "size": 5418, "crc": -779119635}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$6.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$6.class", "size": 5365, "crc": -865493594}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "size": 2192, "crc": 993088480}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$9$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$9$1.class", "size": 3333, "crc": 1804208582}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$9.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$9.class", "size": 7081, "crc": -1797566780}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "size": 4261, "crc": -1019645563}, {"key": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "name": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "size": 817, "crc": 1181202735}, {"key": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1.class", "size": 5780, "crc": -1077649742}, {"key": "androidx/compose/material/OutlinedTextFieldKt.class", "name": "androidx/compose/material/OutlinedTextFieldKt.class", "size": 69422, "crc": 1723686576}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1919, "crc": 1306066747}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1916, "crc": 503880289}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$measure$1.class", "size": 3758, "crc": -377082672}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1919, "crc": 1533611696}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1916, "crc": 542477631}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "size": 24769, "crc": -870082732}, {"key": "androidx/compose/material/ProgressIndicatorDefaults.class", "name": "androidx/compose/material/ProgressIndicatorDefaults.class", "size": 2659, "crc": -1909053422}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "size": 2310, "crc": -1072879525}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$2.class", "size": 2099, "crc": -666058721}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "size": 3584, "crc": -351687760}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$4.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$4.class", "size": 2048, "crc": -1241053731}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$5.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$5.class", "size": 1987, "crc": 68985197}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$6.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$6.class", "size": 1936, "crc": 1486827150}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$endAngle$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$endAngle$2.class", "size": 2659, "crc": -373279166}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$startAngle$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$startAngle$2.class", "size": 2702, "crc": 1260766595}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "size": 2189, "crc": -1570070369}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$2.class", "size": 2038, "crc": -1413287779}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "size": 3289, "crc": -128402172}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$4.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$4.class", "size": 1983, "crc": -1744612793}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$5.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$5.class", "size": 1985, "crc": 1221494114}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$6.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$6.class", "size": 1930, "crc": -1975725365}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$2.class", "size": 2661, "crc": -495903417}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$2.class", "size": 2663, "crc": -1734415265}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$2.class", "size": 2666, "crc": -1375925937}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$2.class", "size": 2666, "crc": 596217667}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1$1.class", "size": 2060, "crc": 1868247593}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1.class", "size": 3062, "crc": 1802741170}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$2.class", "size": 1654, "crc": -469397395}, {"key": "androidx/compose/material/ProgressIndicatorKt.class", "name": "androidx/compose/material/ProgressIndicatorKt.class", "size": 41303, "crc": 1339648104}, {"key": "androidx/compose/material/RadioButtonColors.class", "name": "androidx/compose/material/RadioButtonColors.class", "size": 1074, "crc": -2079216879}, {"key": "androidx/compose/material/RadioButtonDefaults.class", "name": "androidx/compose/material/RadioButtonDefaults.class", "size": 4708, "crc": 1215207714}, {"key": "androidx/compose/material/RadioButtonKt$RadioButton$1$1.class", "name": "androidx/compose/material/RadioButtonKt$RadioButton$1$1.class", "size": 4062, "crc": 208482033}, {"key": "androidx/compose/material/RadioButtonKt$RadioButton$2.class", "name": "androidx/compose/material/RadioButtonKt$RadioButton$2.class", "size": 2714, "crc": -1208859543}, {"key": "androidx/compose/material/RadioButtonKt.class", "name": "androidx/compose/material/RadioButtonKt.class", "size": 11066, "crc": 1710341246}, {"key": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "name": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "size": 4040, "crc": -1606759215}, {"key": "androidx/compose/material/RangeSliderLogic.class", "name": "androidx/compose/material/RangeSliderLogic.class", "size": 5218, "crc": -1474661284}, {"key": "androidx/compose/material/ResistanceConfig.class", "name": "androidx/compose/material/ResistanceConfig.class", "size": 4017, "crc": -1769714983}, {"key": "androidx/compose/material/RippleConfiguration.class", "name": "androidx/compose/material/RippleConfiguration.class", "size": 3339, "crc": 1893006429}, {"key": "androidx/compose/material/RippleDefaults.class", "name": "androidx/compose/material/RippleDefaults.class", "size": 2157, "crc": -1716978987}, {"key": "androidx/compose/material/RippleKt$LocalRippleConfiguration$1.class", "name": "androidx/compose/material/RippleKt$LocalRippleConfiguration$1.class", "size": 1430, "crc": 116224594}, {"key": "androidx/compose/material/RippleKt$LocalUseFallbackRippleImplementation$1.class", "name": "androidx/compose/material/RippleKt$LocalUseFallbackRippleImplementation$1.class", "size": 1254, "crc": 411652517}, {"key": "androidx/compose/material/RippleKt.class", "name": "androidx/compose/material/RippleKt.class", "size": 8631, "crc": -275458449}, {"key": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "name": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "size": 1193, "crc": 1840732205}, {"key": "androidx/compose/material/RippleNodeFactory.class", "name": "androidx/compose/material/RippleNodeFactory.class", "size": 4316, "crc": 458192905}, {"key": "androidx/compose/material/ScaffoldDefaults.class", "name": "androidx/compose/material/ScaffoldDefaults.class", "size": 2401, "crc": 1314658214}, {"key": "androidx/compose/material/ScaffoldKt$LocalFabPlacement$1.class", "name": "androidx/compose/material/ScaffoldKt$LocalFabPlacement$1.class", "size": 1232, "crc": -2144267052}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "size": 3321, "crc": 1998093861}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$2.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$2.class", "size": 5256, "crc": 745657384}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$3.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$3.class", "size": 4996, "crc": 1112304386}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$1$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$1$1.class", "size": 2089, "crc": -1683178147}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "size": 3231, "crc": 147666758}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "size": 5206, "crc": -326794009}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "size": 8508, "crc": 1816505452}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$1.class", "size": 5892, "crc": -1309304210}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "size": 5929, "crc": -248699942}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "size": 3468, "crc": -1965839181}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1.class", "size": 16220, "crc": 1074475280}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$2.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$2.class", "size": 3484, "crc": -421028224}, {"key": "androidx/compose/material/ScaffoldKt.class", "name": "androidx/compose/material/ScaffoldKt.class", "size": 28317, "crc": -540319550}, {"key": "androidx/compose/material/ScaffoldLayoutContent.class", "name": "androidx/compose/material/ScaffoldLayoutContent.class", "size": 1608, "crc": 323968932}, {"key": "androidx/compose/material/ScaffoldState.class", "name": "androidx/compose/material/ScaffoldState.class", "size": 1450, "crc": 2007920401}, {"key": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "name": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "size": 3945, "crc": -1058893791}, {"key": "androidx/compose/material/ScrollableTabData.class", "name": "androidx/compose/material/ScrollableTabData.class", "size": 4233, "crc": -2081636977}, {"key": "androidx/compose/material/SelectableChipColors.class", "name": "androidx/compose/material/SelectableChipColors.class", "size": 1314, "crc": -1150487906}, {"key": "androidx/compose/material/Shapes.class", "name": "androidx/compose/material/Shapes.class", "size": 4889, "crc": 471099450}, {"key": "androidx/compose/material/ShapesKt$LocalShapes$1.class", "name": "androidx/compose/material/ShapesKt$LocalShapes$1.class", "size": 1460, "crc": 62426695}, {"key": "androidx/compose/material/ShapesKt.class", "name": "androidx/compose/material/ShapesKt.class", "size": 1371, "crc": -1651129397}, {"key": "androidx/compose/material/SliderColors.class", "name": "androidx/compose/material/SliderColors.class", "size": 1445, "crc": -296108291}, {"key": "androidx/compose/material/SliderDefaults.class", "name": "androidx/compose/material/SliderDefaults.class", "size": 4434, "crc": 1355538634}, {"key": "androidx/compose/material/SliderDraggableState$drag$2.class", "name": "androidx/compose/material/SliderDraggableState$drag$2.class", "size": 4706, "crc": -111990710}, {"key": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "name": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "size": 1345, "crc": -1304677312}, {"key": "androidx/compose/material/SliderDraggableState.class", "name": "androidx/compose/material/SliderDraggableState.class", "size": 6201, "crc": 124381668}, {"key": "androidx/compose/material/SliderKt$CorrectValueSideEffect$1$1.class", "name": "androidx/compose/material/SliderKt$CorrectValueSideEffect$1$1.class", "size": 3003, "crc": 749791360}, {"key": "androidx/compose/material/SliderKt$CorrectValueSideEffect$2.class", "name": "androidx/compose/material/SliderKt$CorrectValueSideEffect$2.class", "size": 2777, "crc": 1257545125}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "size": 2416, "crc": 947383321}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "size": 2416, "crc": 2131636746}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$endThumbSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$endThumbSemantics$1$1.class", "size": 2029, "crc": -1605228195}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1$1.class", "size": 4025, "crc": -664344895}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "size": 6253, "crc": 1987435504}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1.class", "size": 4438, "crc": 1914120927}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$onDrag$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$onDrag$1$1.class", "size": 4342, "crc": -1841177077}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$startThumbSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$startThumbSemantics$1$1.class", "size": 2031, "crc": 1931759813}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2.class", "size": 20033, "crc": -379875004}, {"key": "androidx/compose/material/SliderKt$RangeSlider$3.class", "name": "androidx/compose/material/SliderKt$RangeSlider$3.class", "size": 3235, "crc": -829540921}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$1$2$1.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$1$2$1.class", "size": 2057, "crc": -678302043}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$1$3$1.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$1$3$1.class", "size": 2055, "crc": 472588198}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$2.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$2.class", "size": 3350, "crc": 849285505}, {"key": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "size": 2401, "crc": -902205132}, {"key": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "size": 3578, "crc": -1021439141}, {"key": "androidx/compose/material/SliderKt$Slider$2$draggableState$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$draggableState$1$1.class", "size": 3217, "crc": -920225551}, {"key": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "size": 4205, "crc": 1905422071}, {"key": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1.class", "size": 3661, "crc": -1936891336}, {"key": "androidx/compose/material/SliderKt$Slider$2.class", "name": "androidx/compose/material/SliderKt$Slider$2.class", "size": 17947, "crc": 1612090803}, {"key": "androidx/compose/material/SliderKt$Slider$3.class", "name": "androidx/compose/material/SliderKt$Slider$3.class", "size": 3322, "crc": 907117734}, {"key": "androidx/compose/material/SliderKt$SliderImpl$2.class", "name": "androidx/compose/material/SliderKt$SliderImpl$2.class", "size": 2639, "crc": 234335331}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "size": 3359, "crc": 405971407}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "size": 4506, "crc": -2146478940}, {"key": "androidx/compose/material/SliderKt$SliderThumb$2.class", "name": "androidx/compose/material/SliderKt$SliderThumb$2.class", "size": 2520, "crc": 1574522605}, {"key": "androidx/compose/material/SliderKt$Track$1$1.class", "name": "androidx/compose/material/SliderKt$Track$1$1.class", "size": 9211, "crc": 829621942}, {"key": "androidx/compose/material/SliderKt$Track$2.class", "name": "androidx/compose/material/SliderKt$Track$2.class", "size": 2425, "crc": -298303136}, {"key": "androidx/compose/material/SliderKt$animateToTarget$2$1.class", "name": "androidx/compose/material/SliderKt$animateToTarget$2$1.class", "size": 2299, "crc": -1186162988}, {"key": "androidx/compose/material/SliderKt$animateToTarget$2.class", "name": "androidx/compose/material/SliderKt$animateToTarget$2.class", "size": 4606, "crc": 1756505633}, {"key": "androidx/compose/material/SliderKt$awaitSlop$1.class", "name": "androidx/compose/material/SliderKt$awaitSlop$1.class", "size": 1559, "crc": -1029605291}, {"key": "androidx/compose/material/SliderKt$awaitSlop$postPointerSlop$1.class", "name": "androidx/compose/material/SliderKt$awaitSlop$postPointerSlop$1.class", "size": 2004, "crc": -1202340557}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "size": 4530, "crc": -54930174}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "size": 3078, "crc": 1753160198}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "size": 10995, "crc": -1053520409}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "size": 5431, "crc": -269972352}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "size": 6283, "crc": 2030920989}, {"key": "androidx/compose/material/SliderKt$sliderSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$sliderSemantics$1$1.class", "size": 3181, "crc": 226169723}, {"key": "androidx/compose/material/SliderKt$sliderSemantics$1.class", "name": "androidx/compose/material/SliderKt$sliderSemantics$1.class", "size": 3041, "crc": 1751577336}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "size": 4134, "crc": -379187527}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "size": 4633, "crc": -110542871}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "size": 3499, "crc": 634735180}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "size": 4591, "crc": -1189904303}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2.class", "size": 2643, "crc": 1597869061}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "size": 5590, "crc": -1139610074}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "size": 9050, "crc": -1962952296}, {"key": "androidx/compose/material/SliderKt.class", "name": "androidx/compose/material/SliderKt.class", "size": 63647, "crc": 1120653076}, {"key": "androidx/compose/material/SnackbarData.class", "name": "androidx/compose/material/SnackbarData.class", "size": 958, "crc": -1538711011}, {"key": "androidx/compose/material/SnackbarDefaults.class", "name": "androidx/compose/material/SnackbarDefaults.class", "size": 3173, "crc": -1532909046}, {"key": "androidx/compose/material/SnackbarDuration.class", "name": "androidx/compose/material/SnackbarDuration.class", "size": 1472, "crc": 1299944789}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "size": 1569, "crc": 1659305441}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "size": 2431, "crc": -318052123}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "size": 2162, "crc": 860396100}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "size": 2434, "crc": -112401501}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "size": 14210, "crc": 2024066219}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "size": 3309, "crc": -84906340}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "size": 2525, "crc": -1566037489}, {"key": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "size": 4317, "crc": -1418574755}, {"key": "androidx/compose/material/SnackbarHostKt$SnackbarHost$2.class", "name": "androidx/compose/material/SnackbarHostKt$SnackbarHost$2.class", "size": 2481, "crc": -794132286}, {"key": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "name": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "size": 864, "crc": 827216601}, {"key": "androidx/compose/material/SnackbarHostKt$animatedOpacity$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedOpacity$1.class", "size": 1191, "crc": -66814767}, {"key": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "size": 4809, "crc": -1326674671}, {"key": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "size": 4481, "crc": -1298896117}, {"key": "androidx/compose/material/SnackbarHostKt.class", "name": "androidx/compose/material/SnackbarHostKt.class", "size": 24976, "crc": 2020732448}, {"key": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "name": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "size": 3039, "crc": 1218629333}, {"key": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "name": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "size": 2060, "crc": -1456431221}, {"key": "androidx/compose/material/SnackbarHostState.class", "name": "androidx/compose/material/SnackbarHostState.class", "size": 8212, "crc": 153201805}, {"key": "androidx/compose/material/SnackbarKt$NewLineButtonSnackbar$2.class", "name": "androidx/compose/material/SnackbarKt$NewLineButtonSnackbar$2.class", "size": 2206, "crc": -874319124}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1$2.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1$2.class", "size": 2208, "crc": -1410448519}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "size": 6940, "crc": -902404301}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$3.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$3.class", "size": 2178, "crc": -114072861}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "size": 3417, "crc": 593337146}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "size": 3677, "crc": 2042204452}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "size": 4065, "crc": 128644295}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$2.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$2.class", "size": 2805, "crc": -1121653441}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "size": 3018, "crc": 1006644129}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$4.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$4.class", "size": 2365, "crc": 1438216194}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "size": 1330, "crc": -1810454084}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "size": 3143, "crc": 516486309}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "size": 5779, "crc": 1785959811}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$2.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$2.class", "size": 3372, "crc": 644890415}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2.class", "size": 4789, "crc": -1074240617}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$3.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$3.class", "size": 1967, "crc": 517827271}, {"key": "androidx/compose/material/SnackbarKt.class", "name": "androidx/compose/material/SnackbarKt.class", "size": 35395, "crc": -2084781105}, {"key": "androidx/compose/material/SnackbarResult.class", "name": "androidx/compose/material/SnackbarResult.class", "size": 1411, "crc": -1855784016}, {"key": "androidx/compose/material/Strings$Companion.class", "name": "androidx/compose/material/Strings$Companion.class", "size": 2174, "crc": -738231334}, {"key": "androidx/compose/material/Strings.class", "name": "androidx/compose/material/Strings.class", "size": 3290, "crc": 1676890046}, {"key": "androidx/compose/material/Strings_androidKt.class", "name": "androidx/compose/material/Strings_androidKt.class", "size": 4154, "crc": 577489476}, {"key": "androidx/compose/material/SurfaceKt$Surface$1$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1$1.class", "size": 1710, "crc": -952838967}, {"key": "androidx/compose/material/SurfaceKt$Surface$1$2.class", "name": "androidx/compose/material/SurfaceKt$Surface$1$2.class", "size": 3174, "crc": -232119846}, {"key": "androidx/compose/material/SurfaceKt$Surface$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1.class", "size": 11132, "crc": 1160817943}, {"key": "androidx/compose/material/SurfaceKt$Surface$2.class", "name": "androidx/compose/material/SurfaceKt$Surface$2.class", "size": 2720, "crc": 941954123}, {"key": "androidx/compose/material/SurfaceKt$Surface$3.class", "name": "androidx/compose/material/SurfaceKt$Surface$3.class", "size": 11809, "crc": 1910488782}, {"key": "androidx/compose/material/SurfaceKt$Surface$4.class", "name": "androidx/compose/material/SurfaceKt$Surface$4.class", "size": 3355, "crc": 1391462143}, {"key": "androidx/compose/material/SurfaceKt$Surface$5.class", "name": "androidx/compose/material/SurfaceKt$Surface$5.class", "size": 11861, "crc": -1365765893}, {"key": "androidx/compose/material/SurfaceKt$Surface$6.class", "name": "androidx/compose/material/SurfaceKt$Surface$6.class", "size": 3465, "crc": 1781550315}, {"key": "androidx/compose/material/SurfaceKt$Surface$7.class", "name": "androidx/compose/material/SurfaceKt$Surface$7.class", "size": 11942, "crc": 1477214524}, {"key": "androidx/compose/material/SurfaceKt$Surface$8.class", "name": "androidx/compose/material/SurfaceKt$Surface$8.class", "size": 3511, "crc": 275176633}, {"key": "androidx/compose/material/SurfaceKt.class", "name": "androidx/compose/material/SurfaceKt.class", "size": 22699, "crc": 384341955}, {"key": "androidx/compose/material/SwipeProgress.class", "name": "androidx/compose/material/SwipeProgress.class", "size": 2939, "crc": -140797234}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$1.class", "size": 1852, "crc": -1197416942}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$1$1$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$1$1$1.class", "size": 2142, "crc": 1981850895}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$thresholds$1$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$thresholds$1$1.class", "size": 2641, "crc": 860652001}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "size": 20111, "crc": -1200109714}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$3.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$3.class", "size": 3407, "crc": 158507860}, {"key": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$1.class", "name": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$1.class", "size": 1642, "crc": 172674208}, {"key": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$2$1.class", "name": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$2$1.class", "size": 1932, "crc": 2054063767}, {"key": "androidx/compose/material/SwipeToDismissKt.class", "name": "androidx/compose/material/SwipeToDismissKt.class", "size": 11022, "crc": -1630135264}, {"key": "androidx/compose/material/SwipeableDefaults.class", "name": "androidx/compose/material/SwipeableDefaults.class", "size": 4113, "crc": 1252334628}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "size": 2011, "crc": 213633891}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "size": 2005, "crc": 1043253865}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "size": 5909, "crc": -1088781065}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableState$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableState$1.class", "size": 1541, "crc": 1605241401}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableState$2$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableState$2$1.class", "size": 2225, "crc": 187730278}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "size": 4168, "crc": -1881405721}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1$invoke$$inlined$onDispose$1.class", "size": 1959, "crc": -768562103}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1.class", "size": 4180, "crc": 874784084}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$swipeableState$1$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$swipeableState$1$1.class", "size": 1710, "crc": 22339900}, {"key": "androidx/compose/material/SwipeableKt$swipeable$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$1.class", "size": 2776, "crc": -359834940}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$3$1$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$3$1$1.class", "size": 3110, "crc": -1533918005}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "size": 5445, "crc": -2049290766}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "size": 3664, "crc": 423861719}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "size": 3835, "crc": -1843311963}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3.class", "size": 9884, "crc": -444058229}, {"key": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "size": 4406, "crc": -44795929}, {"key": "androidx/compose/material/SwipeableKt.class", "name": "androidx/compose/material/SwipeableKt.class", "size": 21966, "crc": 124118849}, {"key": "androidx/compose/material/SwipeableState$1.class", "name": "androidx/compose/material/SwipeableState$1.class", "size": 1507, "crc": 1257603226}, {"key": "androidx/compose/material/SwipeableState$Companion$Saver$1.class", "name": "androidx/compose/material/SwipeableState$Companion$Saver$1.class", "size": 2144, "crc": 552826257}, {"key": "androidx/compose/material/SwipeableState$Companion$Saver$2.class", "name": "androidx/compose/material/SwipeableState$Companion$Saver$2.class", "size": 2362, "crc": -828251056}, {"key": "androidx/compose/material/SwipeableState$Companion.class", "name": "androidx/compose/material/SwipeableState$Companion.class", "size": 2395, "crc": 2143549073}, {"key": "androidx/compose/material/SwipeableState$animateInternalToOffset$2$1.class", "name": "androidx/compose/material/SwipeableState$animateInternalToOffset$2$1.class", "size": 2359, "crc": 1079188511}, {"key": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "size": 5726, "crc": 1661815252}, {"key": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "size": 1896, "crc": -822253610}, {"key": "androidx/compose/material/SwipeableState$animateTo$2.class", "name": "androidx/compose/material/SwipeableState$animateTo$2.class", "size": 7078, "crc": 1199604862}, {"key": "androidx/compose/material/SwipeableState$draggableState$1.class", "name": "androidx/compose/material/SwipeableState$draggableState$1.class", "size": 2462, "crc": 177726942}, {"key": "androidx/compose/material/SwipeableState$latestNonEmptyAnchorsFlow$1.class", "name": "androidx/compose/material/SwipeableState$latestNonEmptyAnchorsFlow$1.class", "size": 1602, "crc": 1755862528}, {"key": "androidx/compose/material/SwipeableState$performFling$2.class", "name": "androidx/compose/material/SwipeableState$performFling$2.class", "size": 4042, "crc": 1117724005}, {"key": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "name": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "size": 1957, "crc": 1283285324}, {"key": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "size": 3884, "crc": 2079764843}, {"key": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "size": 1836, "crc": -1804745598}, {"key": "androidx/compose/material/SwipeableState$snapTo$2.class", "name": "androidx/compose/material/SwipeableState$snapTo$2.class", "size": 3660, "crc": -831941691}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "size": 2102, "crc": 2024681516}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "size": 3750, "crc": -1585735332}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "size": 3203, "crc": -243131386}, {"key": "androidx/compose/material/SwipeableState$thresholds$2.class", "name": "androidx/compose/material/SwipeableState$thresholds$2.class", "size": 1599, "crc": -885785517}, {"key": "androidx/compose/material/SwipeableState.class", "name": "androidx/compose/material/SwipeableState.class", "size": 27987, "crc": -860276597}, {"key": "androidx/compose/material/SwitchColors.class", "name": "androidx/compose/material/SwitchColors.class", "size": 1163, "crc": -689561177}, {"key": "androidx/compose/material/SwitchDefaults.class", "name": "androidx/compose/material/SwitchDefaults.class", "size": 4085, "crc": -703060271}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1$1.class", "size": 1568, "crc": -216963110}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "size": 4598, "crc": -367963439}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1.class", "size": 5216, "crc": -1588995672}, {"key": "androidx/compose/material/SwitchKt$Switch$2$1.class", "name": "androidx/compose/material/SwitchKt$Switch$2$1.class", "size": 4176, "crc": 1179702799}, {"key": "androidx/compose/material/SwitchKt$Switch$3$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$3$1$1.class", "size": 1755, "crc": 209476730}, {"key": "androidx/compose/material/SwitchKt$Switch$4.class", "name": "androidx/compose/material/SwitchKt$Switch$4.class", "size": 2705, "crc": 408788014}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$1.class", "size": 2015, "crc": 389756771}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$2.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$2.class", "size": 1614, "crc": -791320125}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$3.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$3.class", "size": 1446, "crc": 656555453}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "size": 3350, "crc": 1853346981}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "size": 4448, "crc": -1047111326}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$2$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$2$1.class", "size": 2214, "crc": -1645545490}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$3$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$3$1.class", "size": 2199, "crc": -766882777}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$4.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$4.class", "size": 2704, "crc": -1511216339}, {"key": "androidx/compose/material/SwitchKt.class", "name": "androidx/compose/material/SwitchKt.class", "size": 36947, "crc": 69802678}, {"key": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "name": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "size": 2065, "crc": -393573168}, {"key": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "name": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "size": 13701, "crc": 1262322015}, {"key": "androidx/compose/material/TabKt$LeadingIconTab$2.class", "name": "androidx/compose/material/TabKt$LeadingIconTab$2.class", "size": 3185, "crc": 878434432}, {"key": "androidx/compose/material/TabKt$Tab$1.class", "name": "androidx/compose/material/TabKt$Tab$1.class", "size": 3350, "crc": 2134530166}, {"key": "androidx/compose/material/TabKt$Tab$2.class", "name": "androidx/compose/material/TabKt$Tab$2.class", "size": 3152, "crc": 1391509966}, {"key": "androidx/compose/material/TabKt$Tab$3.class", "name": "androidx/compose/material/TabKt$Tab$3.class", "size": 10795, "crc": 2088946493}, {"key": "androidx/compose/material/TabKt$Tab$4.class", "name": "androidx/compose/material/TabKt$Tab$4.class", "size": 3031, "crc": -102001725}, {"key": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "name": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "size": 4389, "crc": 1720902010}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$2$1$1.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$2$1$1.class", "size": 2975, "crc": 1126721069}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "size": 6875, "crc": -1075608249}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$3.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$3.class", "size": 2168, "crc": -2039636011}, {"key": "androidx/compose/material/TabKt$TabTransition$1.class", "name": "androidx/compose/material/TabKt$TabTransition$1.class", "size": 2126, "crc": 1602388349}, {"key": "androidx/compose/material/TabKt$TabTransition$color$2.class", "name": "androidx/compose/material/TabKt$TabTransition$color$2.class", "size": 3632, "crc": -765427189}, {"key": "androidx/compose/material/TabKt.class", "name": "androidx/compose/material/TabKt.class", "size": 37326, "crc": -836486688}, {"key": "androidx/compose/material/TabPosition.class", "name": "androidx/compose/material/TabPosition.class", "size": 3257, "crc": 1242133794}, {"key": "androidx/compose/material/TabRowDefaults$Divider$1.class", "name": "androidx/compose/material/TabRowDefaults$Divider$1.class", "size": 1991, "crc": 428226160}, {"key": "androidx/compose/material/TabRowDefaults$Indicator$1.class", "name": "androidx/compose/material/TabRowDefaults$Indicator$1.class", "size": 1994, "crc": 428923495}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "size": 2779, "crc": -872264822}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "size": 2103, "crc": 1533237692}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "size": 7276, "crc": -181322092}, {"key": "androidx/compose/material/TabRowDefaults.class", "name": "androidx/compose/material/TabRowDefaults.class", "size": 9468, "crc": -2074385613}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "size": 3188, "crc": -1547871157}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "size": 3351, "crc": 879544501}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2.class", "size": 7433, "crc": 585261368}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1.class", "size": 6961, "crc": -1922437833}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "size": 9976, "crc": -924127209}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$3.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$3.class", "size": 3104, "crc": 986374794}, {"key": "androidx/compose/material/TabRowKt$TabRow$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$1.class", "size": 3146, "crc": -938817842}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "size": 3290, "crc": 1669626147}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1.class", "size": 6420, "crc": 1806320233}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1.class", "size": 7137, "crc": -1869911056}, {"key": "androidx/compose/material/TabRowKt$TabRow$2.class", "name": "androidx/compose/material/TabRowKt$TabRow$2.class", "size": 5580, "crc": 672159205}, {"key": "androidx/compose/material/TabRowKt$TabRow$3.class", "name": "androidx/compose/material/TabRowKt$TabRow$3.class", "size": 3014, "crc": -464659468}, {"key": "androidx/compose/material/TabRowKt.class", "name": "androidx/compose/material/TabRowKt.class", "size": 11154, "crc": -826548208}, {"key": "androidx/compose/material/TabSlots.class", "name": "androidx/compose/material/TabSlots.class", "size": 1419, "crc": 1358554375}, {"key": "androidx/compose/material/TextFieldColors.class", "name": "androidx/compose/material/TextFieldColors.class", "size": 4340, "crc": 1002290379}, {"key": "androidx/compose/material/TextFieldColorsWithIcons.class", "name": "androidx/compose/material/TextFieldColorsWithIcons.class", "size": 866, "crc": -1475570379}, {"key": "androidx/compose/material/TextFieldDefaults$BorderBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$BorderBox$1.class", "size": 2591, "crc": -345633004}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "size": 3553, "crc": -727585239}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "size": 4979, "crc": -326394272}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "size": 3396, "crc": -1630322592}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "size": 4785, "crc": -1484138922}, {"key": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$1.class", "size": 4732, "crc": -1711829966}, {"key": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$2.class", "name": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$2.class", "size": 4538, "crc": 658238642}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "size": 3940, "crc": -689291894}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "size": 3849, "crc": -1526795600}, {"key": "androidx/compose/material/TextFieldDefaults.class", "name": "androidx/compose/material/TextFieldDefaults.class", "size": 42986, "crc": -321397508}, {"key": "androidx/compose/material/TextFieldDefaultsKt.class", "name": "androidx/compose/material/TextFieldDefaultsKt.class", "size": 6164, "crc": -897572500}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "size": 2293, "crc": 1110462646}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "size": 865, "crc": -574200091}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "size": 4884, "crc": 658168086}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "size": 2984, "crc": -1699840882}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "size": 10686, "crc": 878232560}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "size": 2987, "crc": 1381968688}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "size": 10267, "crc": 806693727}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "size": 14373, "crc": 1123900418}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$4.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$4.class", "size": 4907, "crc": -338923377}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "size": 3936, "crc": 549759943}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$1.class", "size": 2384, "crc": 766657121}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "size": 3573, "crc": -1163850519}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "size": 3848, "crc": 1024371721}, {"key": "androidx/compose/material/TextFieldImplKt$defaultErrorSemantics$1.class", "name": "androidx/compose/material/TextFieldImplKt$defaultErrorSemantics$1.class", "size": 1834, "crc": -1678011556}, {"key": "androidx/compose/material/TextFieldImplKt.class", "name": "androidx/compose/material/TextFieldImplKt.class", "size": 21721, "crc": -1868204516}, {"key": "androidx/compose/material/TextFieldKt$TextField$1.class", "name": "androidx/compose/material/TextFieldKt$TextField$1.class", "size": 6214, "crc": -826874767}, {"key": "androidx/compose/material/TextFieldKt$TextField$2.class", "name": "androidx/compose/material/TextFieldKt$TextField$2.class", "size": 5362, "crc": -908076139}, {"key": "androidx/compose/material/TextFieldKt$TextField$4.class", "name": "androidx/compose/material/TextFieldKt$TextField$4.class", "size": 5309, "crc": 504916805}, {"key": "androidx/compose/material/TextFieldKt$TextField$5.class", "name": "androidx/compose/material/TextFieldKt$TextField$5.class", "size": 6443, "crc": 2045990627}, {"key": "androidx/compose/material/TextFieldKt$TextField$6.class", "name": "androidx/compose/material/TextFieldKt$TextField$6.class", "size": 5536, "crc": 1927199089}, {"key": "androidx/compose/material/TextFieldKt$TextField$8.class", "name": "androidx/compose/material/TextFieldKt$TextField$8.class", "size": 5483, "crc": -121911535}, {"key": "androidx/compose/material/TextFieldKt$TextFieldLayout$2.class", "name": "androidx/compose/material/TextFieldKt$TextFieldLayout$2.class", "size": 3593, "crc": -957290063}, {"key": "androidx/compose/material/TextFieldKt$drawIndicatorLine$1.class", "name": "androidx/compose/material/TextFieldKt$drawIndicatorLine$1.class", "size": 2839, "crc": 530118524}, {"key": "androidx/compose/material/TextFieldKt.class", "name": "androidx/compose/material/TextFieldKt.class", "size": 66783, "crc": -189549991}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1887, "crc": -1214057849}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1884, "crc": -729786753}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$measure$1.class", "size": 4139, "crc": -703860829}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1887, "crc": 2069667850}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1884, "crc": 619292805}, {"key": "androidx/compose/material/TextFieldMeasurePolicy.class", "name": "androidx/compose/material/TextFieldMeasurePolicy.class", "size": 22655, "crc": 1567006400}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$1.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$1.class", "size": 3292, "crc": 1517061576}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "size": 3497, "crc": -792068591}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "size": 3415, "crc": 181686373}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "size": 3501, "crc": 1379029534}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "size": 4042, "crc": -221217090}, {"key": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "name": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "size": 886, "crc": -504132516}, {"key": "androidx/compose/material/TextFieldTransitionScope.class", "name": "androidx/compose/material/TextFieldTransitionScope.class", "size": 17592, "crc": -252997568}, {"key": "androidx/compose/material/TextFieldType.class", "name": "androidx/compose/material/TextFieldType.class", "size": 1392, "crc": 1818440611}, {"key": "androidx/compose/material/TextKt$LocalTextStyle$1.class", "name": "androidx/compose/material/TextKt$LocalTextStyle$1.class", "size": 1273, "crc": -1208801161}, {"key": "androidx/compose/material/TextKt$ProvideTextStyle$1.class", "name": "androidx/compose/material/TextKt$ProvideTextStyle$1.class", "size": 2113, "crc": -513403047}, {"key": "androidx/compose/material/TextKt$Text$1$1.class", "name": "androidx/compose/material/TextKt$Text$1$1.class", "size": 1249, "crc": -1324586895}, {"key": "androidx/compose/material/TextKt$Text$2.class", "name": "androidx/compose/material/TextKt$Text$2.class", "size": 4040, "crc": 272411840}, {"key": "androidx/compose/material/TextKt$Text$3.class", "name": "androidx/compose/material/TextKt$Text$3.class", "size": 1709, "crc": 1997996224}, {"key": "androidx/compose/material/TextKt$Text$4.class", "name": "androidx/compose/material/TextKt$Text$4.class", "size": 3987, "crc": 2056635683}, {"key": "androidx/compose/material/TextKt$Text$5.class", "name": "androidx/compose/material/TextKt$Text$5.class", "size": 1749, "crc": -1895365499}, {"key": "androidx/compose/material/TextKt$Text$6$1.class", "name": "androidx/compose/material/TextKt$Text$6$1.class", "size": 1288, "crc": 548491247}, {"key": "androidx/compose/material/TextKt$Text$7.class", "name": "androidx/compose/material/TextKt$Text$7.class", "size": 4424, "crc": 1427820232}, {"key": "androidx/compose/material/TextKt$Text$8.class", "name": "androidx/compose/material/TextKt$Text$8.class", "size": 1748, "crc": -418970565}, {"key": "androidx/compose/material/TextKt$Text$9.class", "name": "androidx/compose/material/TextKt$Text$9.class", "size": 4371, "crc": 18640318}, {"key": "androidx/compose/material/TextKt.class", "name": "androidx/compose/material/TextKt.class", "size": 32974, "crc": -605942682}, {"key": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "name": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "size": 3409, "crc": -1370209901}, {"key": "androidx/compose/material/ThreeLine$ListItem$2.class", "name": "androidx/compose/material/ThreeLine$ListItem$2.class", "size": 3201, "crc": 498366421}, {"key": "androidx/compose/material/ThreeLine.class", "name": "androidx/compose/material/ThreeLine.class", "size": 18524, "crc": 1697092605}, {"key": "androidx/compose/material/ThresholdConfig.class", "name": "androidx/compose/material/ThresholdConfig.class", "size": 1040, "crc": -1106988274}, {"key": "androidx/compose/material/TwoLine$ListItem$1$2.class", "name": "androidx/compose/material/TwoLine$ListItem$1$2.class", "size": 3055, "crc": 924624246}, {"key": "androidx/compose/material/TwoLine$ListItem$1$3.class", "name": "androidx/compose/material/TwoLine$ListItem$1$3.class", "size": 3145, "crc": -1159436385}, {"key": "androidx/compose/material/TwoLine$ListItem$1$4.class", "name": "androidx/compose/material/TwoLine$ListItem$1$4.class", "size": 9421, "crc": -662032029}, {"key": "androidx/compose/material/TwoLine$ListItem$2.class", "name": "androidx/compose/material/TwoLine$ListItem$2.class", "size": 3189, "crc": -955352014}, {"key": "androidx/compose/material/TwoLine.class", "name": "androidx/compose/material/TwoLine.class", "size": 19650, "crc": -405100669}, {"key": "androidx/compose/material/Typography.class", "name": "androidx/compose/material/Typography.class", "size": 13002, "crc": 101405985}, {"key": "androidx/compose/material/TypographyKt$LocalTypography$1.class", "name": "androidx/compose/material/TypographyKt$LocalTypography$1.class", "size": 1866, "crc": 1449971045}, {"key": "androidx/compose/material/TypographyKt.class", "name": "androidx/compose/material/TypographyKt.class", "size": 4468, "crc": 812156576}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "size": 2393, "crc": 2107543419}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "size": 1644, "crc": 111829067}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "size": 2462, "crc": 1704971933}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "size": 4184, "crc": -1862029322}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "size": 2277, "crc": 859401210}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1$invoke$$inlined$onDispose$1.class", "size": 2182, "crc": 1763973639}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1.class", "size": 3661, "crc": 1045807618}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "size": 2888, "crc": -2089345278}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1$1.class", "size": 1868, "crc": 1050635337}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "size": 2784, "crc": -2095240229}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$7.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$7.class", "size": 2663, "crc": -1661705736}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "size": 1502, "crc": -1975570214}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "size": 1860, "crc": -299619389}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "size": 1960, "crc": 1580007531}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$3.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$3.class", "size": 3208, "crc": -1604256093}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "size": 11630, "crc": 1037758749}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$LocalPopupTestTag$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$LocalPopupTestTag$1.class", "size": 1291, "crc": -68198497}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "size": 2364, "crc": 746026812}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "size": 2575, "crc": 2138074152}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "size": 2977, "crc": -225500834}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "size": 5360, "crc": -226318780}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "size": 21083, "crc": 731355938}, {"key": "androidx/compose/material/internal/PopupLayout$2.class", "name": "androidx/compose/material/internal/PopupLayout$2.class", "size": 1456, "crc": -1513115444}, {"key": "androidx/compose/material/internal/PopupLayout$Content$4.class", "name": "androidx/compose/material/internal/PopupLayout$Content$4.class", "size": 1693, "crc": 326143004}, {"key": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "name": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "size": 837, "crc": -1528302524}, {"key": "androidx/compose/material/internal/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/material/internal/PopupLayout$canCalculatePosition$2.class", "size": 1727, "crc": 2065222780}, {"key": "androidx/compose/material/internal/PopupLayout$dismissOnOutsideClick$1.class", "name": "androidx/compose/material/internal/PopupLayout$dismissOnOutsideClick$1.class", "size": 2340, "crc": -1190565674}, {"key": "androidx/compose/material/internal/PopupLayout.class", "name": "androidx/compose/material/internal/PopupLayout.class", "size": 20479, "crc": -236285966}, {"key": "androidx/compose/material/pullrefresh/ArrowValues.class", "name": "androidx/compose/material/pullrefresh/ArrowValues.class", "size": 1305, "crc": -533778206}, {"key": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "name": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "size": 2254, "crc": -1284857834}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$1.class", "size": 1771, "crc": 476773974}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$2$1.class", "size": 7220, "crc": -1956057443}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$3.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$3.class", "size": 2127, "crc": -1101010945}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$targetAlpha$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$targetAlpha$2$1.class", "size": 1714, "crc": -79321299}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "size": 10977, "crc": 701334033}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$2.class", "size": 2300, "crc": 942592789}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$showElevation$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$showElevation$2$1.class", "size": 1801, "crc": 202872858}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "size": 28376, "crc": 809424954}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$1.class", "size": 5026, "crc": 49457074}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$2.class", "size": 4092, "crc": -506929935}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "size": 2120, "crc": 912932827}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "size": 1663, "crc": 819903191}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "size": 2234, "crc": 579728029}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "size": 3925, "crc": 1509476241}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "size": 1946, "crc": -2121183618}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "size": 5412, "crc": 1153892384}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$adjustedDistancePulled$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$adjustedDistancePulled$2.class", "size": 1563, "crc": -921717440}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1$1.class", "size": 1871, "crc": 1235633010}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "size": 3832, "crc": -261738312}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "size": 4140, "crc": 722725446}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState.class", "size": 11284, "crc": -1477320360}, {"key": "androidx/compose/material/pullrefresh/PullRefreshStateKt$rememberPullRefreshState$3$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshStateKt$rememberPullRefreshState$3$1.class", "size": 2058, "crc": -2129615715}, {"key": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "size": 9659, "crc": 1356725169}, {"key": "META-INF/androidx.compose.material_material.version", "name": "META-INF/androidx.compose.material_material.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/material_release.kotlin_module", "name": "META-INF/material_release.kotlin_module", "size": 1206, "crc": 1647888293}]