package cn.ykload.seeq

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

/**
 * 自定义底部模态框，确保完全展开显示并保持正确的动画效果
 */
class FullExpandBottomSheetDialog(context: Context, theme: Int) : BottomSheetDialog(context, theme) {

    private var isAnimationEnabled = true
    private var bottomSheetBehavior: BottomSheetBehavior<View>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置窗口属性
        window?.let { window ->
            window.setDimAmount(0.5f)
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT
        }
    }

    override fun setContentView(view: View) {
        super.setContentView(view)
        setupFullExpand()
    }

    override fun setContentView(layoutResId: Int) {
        super.setContentView(layoutResId)
        setupFullExpand()
    }

    override fun setContentView(view: View, params: ViewGroup.LayoutParams?) {
        super.setContentView(view, params)
        setupFullExpand()
    }

    private fun setupFullExpand() {
        // 延迟设置，确保视图已经创建
        findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)?.let { bottomSheet ->
            val behavior = BottomSheetBehavior.from(bottomSheet)
            bottomSheetBehavior = behavior

            // 设置行为属性 - 保持动画效果
            behavior.isHideable = true
            behavior.isDraggable = true
            behavior.skipCollapsed = false  // 允许折叠状态，保持动画

            // 设置peek高度为屏幕高度的90%，确保有足够空间显示内容
            val displayMetrics = context.resources.displayMetrics
            behavior.peekHeight = (displayMetrics.heightPixels * 0.9).toInt()

            // 设置展开偏移量
            behavior.expandedOffset = 0
            behavior.isFitToContents = true

            // 监听状态变化 - 只在必要时干预，避免破坏动画
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    // 只有在用户试图完全隐藏时才干预，允许其他状态变化
                    if (newState == BottomSheetBehavior.STATE_HIDDEN && isAnimationEnabled) {
                        // 如果用户试图隐藏，则关闭对话框
                        dismiss()
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // 保持滑动动画的流畅性
                    // slideOffset: -1 (hidden) to 1 (expanded)
                }
            })
        }
    }

    override fun show() {
        super.show()

        // 延迟设置展开状态，让初始动画完成
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            try {
                bottomSheetBehavior?.let { behavior ->
                    // 只有在不是已经展开的情况下才设置状态
                    if (behavior.state != BottomSheetBehavior.STATE_EXPANDED) {
                        behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                }
            } catch (e: Exception) {
                android.util.Log.w("FullExpandBottomSheet", "无法设置展开状态", e)
            }
        }, 100) // 增加延迟时间，确保初始动画完成
    }

    override fun dismiss() {
        // 确保关闭动画正常执行
        isAnimationEnabled = false
        super.dismiss()
    }
}
