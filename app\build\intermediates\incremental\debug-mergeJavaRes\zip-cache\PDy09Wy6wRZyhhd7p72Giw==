[{"key": "androidx/webkit/BlockingStartUpLocation.class", "name": "androidx/webkit/BlockingStartUpLocation.class", "size": 494, "crc": -603880294}, {"key": "androidx/webkit/CookieManagerCompat.class", "name": "androidx/webkit/CookieManagerCompat.class", "size": 1807, "crc": **********}, {"key": "androidx/webkit/DropDataContentProvider.class", "name": "androidx/webkit/DropDataContentProvider.class", "size": 3279, "crc": -948160532}, {"key": "androidx/webkit/JavaScriptReplyProxy.class", "name": "androidx/webkit/JavaScriptReplyProxy.class", "size": 786, "crc": -314522917}, {"key": "androidx/webkit/Navigation.class", "name": "androidx/webkit/Navigation.class", "size": 771, "crc": 913463199}, {"key": "androidx/webkit/NoVarySearchHeader.class", "name": "androidx/webkit/NoVarySearchHeader.class", "size": 1953, "crc": -**********}, {"key": "androidx/webkit/OutcomeReceiverCompat.class", "name": "androidx/webkit/OutcomeReceiverCompat.class", "size": 959, "crc": -**********}, {"key": "androidx/webkit/Page.class", "name": "androidx/webkit/Page.class", "size": 395, "crc": -**********}, {"key": "androidx/webkit/Policy$1.class", "name": "androidx/webkit/Policy$1.class", "size": 198, "crc": **********}, {"key": "androidx/webkit/Policy$Builder.class", "name": "androidx/webkit/Policy$Builder.class", "size": 888, "crc": 669130391}, {"key": "androidx/webkit/Policy$ConfigRunnable.class", "name": "androidx/webkit/Policy$ConfigRunnable.class", "size": 534, "crc": -468584598}, {"key": "androidx/webkit/Policy.class", "name": "androidx/webkit/Policy.class", "size": 2136, "crc": -88052348}, {"key": "androidx/webkit/PrefetchException.class", "name": "androidx/webkit/PrefetchException.class", "size": 771, "crc": -238454660}, {"key": "androidx/webkit/PrefetchNetworkException.class", "name": "androidx/webkit/PrefetchNetworkException.class", "size": 1169, "crc": 1009049183}, {"key": "androidx/webkit/PrerenderException.class", "name": "androidx/webkit/PrerenderException.class", "size": 817, "crc": 2070784131}, {"key": "androidx/webkit/PrerenderOperationCallback.class", "name": "androidx/webkit/PrerenderOperationCallback.class", "size": 584, "crc": -1236828343}, {"key": "androidx/webkit/ProcessGlobalConfig.class", "name": "androidx/webkit/ProcessGlobalConfig.class", "size": 6295, "crc": -632740859}, {"key": "androidx/webkit/Profile$ExperimentalUrlPrefetch.class", "name": "androidx/webkit/Profile$ExperimentalUrlPrefetch.class", "size": 763, "crc": 1058732606}, {"key": "androidx/webkit/Profile.class", "name": "androidx/webkit/Profile.class", "size": 2329, "crc": -1061132479}, {"key": "androidx/webkit/ProfileStore.class", "name": "androidx/webkit/ProfileStore.class", "size": 1324, "crc": -1180700332}, {"key": "androidx/webkit/ProxyConfig$Builder.class", "name": "androidx/webkit/ProxyConfig$Builder.class", "size": 3145, "crc": 486782052}, {"key": "androidx/webkit/ProxyConfig$ProxyRule.class", "name": "androidx/webkit/ProxyConfig$ProxyRule.class", "size": 1258, "crc": -679931948}, {"key": "androidx/webkit/ProxyConfig$ProxyScheme.class", "name": "androidx/webkit/ProxyConfig$ProxyScheme.class", "size": 620, "crc": 1155789592}, {"key": "androidx/webkit/ProxyConfig.class", "name": "androidx/webkit/ProxyConfig.class", "size": 2220, "crc": -1611978001}, {"key": "androidx/webkit/ProxyController$LAZY_HOLDER.class", "name": "androidx/webkit/ProxyController$LAZY_HOLDER.class", "size": 588, "crc": 475624606}, {"key": "androidx/webkit/ProxyController.class", "name": "androidx/webkit/ProxyController.class", "size": 1405, "crc": 1417699802}, {"key": "androidx/webkit/SafeBrowsingResponseCompat.class", "name": "androidx/webkit/SafeBrowsingResponseCompat.class", "size": 677, "crc": -941489355}, {"key": "androidx/webkit/ScriptHandler.class", "name": "androidx/webkit/ScriptHandler.class", "size": 221, "crc": 231112901}, {"key": "androidx/webkit/ServiceWorkerClientCompat.class", "name": "androidx/webkit/ServiceWorkerClientCompat.class", "size": 648, "crc": 539511479}, {"key": "androidx/webkit/ServiceWorkerControllerCompat$LAZY_HOLDER.class", "name": "androidx/webkit/ServiceWorkerControllerCompat$LAZY_HOLDER.class", "size": 666, "crc": 951864278}, {"key": "androidx/webkit/ServiceWorkerControllerCompat.class", "name": "androidx/webkit/ServiceWorkerControllerCompat.class", "size": 1190, "crc": 492118445}, {"key": "androidx/webkit/ServiceWorkerWebSettingsCompat$CacheMode.class", "name": "androidx/webkit/ServiceWorkerWebSettingsCompat$CacheMode.class", "size": 673, "crc": 1632058964}, {"key": "androidx/webkit/ServiceWorkerWebSettingsCompat.class", "name": "androidx/webkit/ServiceWorkerWebSettingsCompat.class", "size": 1338, "crc": 734356}, {"key": "androidx/webkit/SpeculativeLoadingConfig$1.class", "name": "androidx/webkit/SpeculativeLoadingConfig$1.class", "size": 252, "crc": -59712535}, {"key": "androidx/webkit/SpeculativeLoadingConfig$Builder.class", "name": "androidx/webkit/SpeculativeLoadingConfig$Builder.class", "size": 1970, "crc": 1167809878}, {"key": "androidx/webkit/SpeculativeLoadingConfig.class", "name": "androidx/webkit/SpeculativeLoadingConfig.class", "size": 1505, "crc": -1771094615}, {"key": "androidx/webkit/SpeculativeLoadingParameters$1.class", "name": "androidx/webkit/SpeculativeLoadingParameters$1.class", "size": 264, "crc": 1169812291}, {"key": "androidx/webkit/SpeculativeLoadingParameters$Builder.class", "name": "androidx/webkit/SpeculativeLoadingParameters$Builder.class", "size": 2734, "crc": 1578761760}, {"key": "androidx/webkit/SpeculativeLoadingParameters.class", "name": "androidx/webkit/SpeculativeLoadingParameters.class", "size": 2149, "crc": 1623619941}, {"key": "androidx/webkit/TracingConfig$Builder.class", "name": "androidx/webkit/TracingConfig$Builder.class", "size": 2082, "crc": -1412695813}, {"key": "androidx/webkit/TracingConfig$PredefinedCategories.class", "name": "androidx/webkit/TracingConfig$PredefinedCategories.class", "size": 644, "crc": -558306872}, {"key": "androidx/webkit/TracingConfig$TracingMode.class", "name": "androidx/webkit/TracingConfig$TracingMode.class", "size": 626, "crc": -641013872}, {"key": "androidx/webkit/TracingConfig.class", "name": "androidx/webkit/TracingConfig.class", "size": 2268, "crc": 634577185}, {"key": "androidx/webkit/TracingController$LAZY_HOLDER.class", "name": "androidx/webkit/TracingController$LAZY_HOLDER.class", "size": 600, "crc": -1372610114}, {"key": "androidx/webkit/TracingController.class", "name": "androidx/webkit/TracingController.class", "size": 1118, "crc": 367412227}, {"key": "androidx/webkit/URLUtilCompat.class", "name": "androidx/webkit/URLUtilCompat.class", "size": 5518, "crc": -196765942}, {"key": "androidx/webkit/UserAgentMetadata$1.class", "name": "androidx/webkit/UserAgentMetadata$1.class", "size": 231, "crc": 1050557160}, {"key": "androidx/webkit/UserAgentMetadata$BrandVersion$Builder.class", "name": "androidx/webkit/UserAgentMetadata$BrandVersion$Builder.class", "size": 2392, "crc": -373814304}, {"key": "androidx/webkit/UserAgentMetadata$BrandVersion.class", "name": "androidx/webkit/UserAgentMetadata$BrandVersion.class", "size": 2446, "crc": -1313140843}, {"key": "androidx/webkit/UserAgentMetadata$Builder.class", "name": "androidx/webkit/UserAgentMetadata$Builder.class", "size": 3918, "crc": 1430980716}, {"key": "androidx/webkit/UserAgentMetadata.class", "name": "androidx/webkit/UserAgentMetadata.class", "size": 4133, "crc": -805331535}, {"key": "androidx/webkit/WebMessageCompat$Type.class", "name": "androidx/webkit/WebMessageCompat$Type.class", "size": 627, "crc": -1487855087}, {"key": "androidx/webkit/WebMessageCompat.class", "name": "androidx/webkit/WebMessageCompat.class", "size": 2634, "crc": 1792853897}, {"key": "androidx/webkit/WebMessagePortCompat$WebMessageCallbackCompat.class", "name": "androidx/webkit/WebMessagePortCompat$WebMessageCallbackCompat.class", "size": 858, "crc": 2110783112}, {"key": "androidx/webkit/WebMessagePortCompat.class", "name": "androidx/webkit/WebMessagePortCompat.class", "size": 1421, "crc": -888764998}, {"key": "androidx/webkit/WebNavigationClient$ExperimentalNavigationCallback.class", "name": "androidx/webkit/WebNavigationClient$ExperimentalNavigationCallback.class", "size": 813, "crc": 183796666}, {"key": "androidx/webkit/WebNavigationClient.class", "name": "androidx/webkit/WebNavigationClient.class", "size": 846, "crc": -1192823121}, {"key": "androidx/webkit/WebResourceErrorCompat$NetErrorCode.class", "name": "androidx/webkit/WebResourceErrorCompat$NetErrorCode.class", "size": 655, "crc": 1265299041}, {"key": "androidx/webkit/WebResourceErrorCompat.class", "name": "androidx/webkit/WebResourceErrorCompat.class", "size": 836, "crc": 1256193422}, {"key": "androidx/webkit/WebResourceRequestCompat.class", "name": "androidx/webkit/WebResourceRequestCompat.class", "size": 1632, "crc": -1926489813}, {"key": "androidx/webkit/WebSettingsCompat$ExperimentalBackForwardCache.class", "name": "androidx/webkit/WebSettingsCompat$ExperimentalBackForwardCache.class", "size": 803, "crc": -94002531}, {"key": "androidx/webkit/WebSettingsCompat$ExperimentalSpeculativeLoading.class", "name": "androidx/webkit/WebSettingsCompat$ExperimentalSpeculativeLoading.class", "size": 807, "crc": -1265787740}, {"key": "androidx/webkit/WebSettingsCompat$ForceDark.class", "name": "androidx/webkit/WebSettingsCompat$ForceDark.class", "size": 743, "crc": -1370511495}, {"key": "androidx/webkit/WebSettingsCompat$ForceDarkStrategy.class", "name": "androidx/webkit/WebSettingsCompat$ForceDarkStrategy.class", "size": 759, "crc": -937473684}, {"key": "androidx/webkit/WebSettingsCompat$MenuItemFlags.class", "name": "androidx/webkit/WebSettingsCompat$MenuItemFlags.class", "size": 751, "crc": -991363901}, {"key": "androidx/webkit/WebSettingsCompat.class", "name": "androidx/webkit/WebSettingsCompat.class", "size": 13193, "crc": 1526486895}, {"key": "androidx/webkit/WebStorageCompat.class", "name": "androidx/webkit/WebStorageCompat.class", "size": 3618, "crc": 1711489173}, {"key": "androidx/webkit/WebViewAssetLoader$AssetsPathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$AssetsPathHandler.class", "size": 2118, "crc": 1027814271}, {"key": "androidx/webkit/WebViewAssetLoader$Builder.class", "name": "androidx/webkit/WebViewAssetLoader$Builder.class", "size": 2790, "crc": -361984370}, {"key": "androidx/webkit/WebViewAssetLoader$InternalStoragePathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$InternalStoragePathHandler.class", "size": 3856, "crc": -1720925059}, {"key": "androidx/webkit/WebViewAssetLoader$PathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$PathHandler.class", "size": 513, "crc": 1552958262}, {"key": "androidx/webkit/WebViewAssetLoader$PathMatcher.class", "name": "androidx/webkit/WebViewAssetLoader$PathMatcher.class", "size": 2453, "crc": -605788043}, {"key": "androidx/webkit/WebViewAssetLoader$ResourcesPathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$ResourcesPathHandler.class", "size": 2415, "crc": -2030279499}, {"key": "androidx/webkit/WebViewAssetLoader.class", "name": "androidx/webkit/WebViewAssetLoader.class", "size": 2606, "crc": 164137894}, {"key": "androidx/webkit/WebViewBuilder$Experimental.class", "name": "androidx/webkit/WebViewBuilder$Experimental.class", "size": 762, "crc": 76391132}, {"key": "androidx/webkit/WebViewBuilder.class", "name": "androidx/webkit/WebViewBuilder.class", "size": 2782, "crc": 760817323}, {"key": "androidx/webkit/WebViewBuilderException.class", "name": "androidx/webkit/WebViewBuilderException.class", "size": 841, "crc": 2128400892}, {"key": "androidx/webkit/WebViewClientCompat$SafeBrowsingThreat.class", "name": "androidx/webkit/WebViewClientCompat$SafeBrowsingThreat.class", "size": 658, "crc": 870297702}, {"key": "androidx/webkit/WebViewClientCompat.class", "name": "androidx/webkit/WebViewClientCompat.class", "size": 5837, "crc": -653571574}, {"key": "androidx/webkit/WebViewCompat$1.class", "name": "androidx/webkit/WebViewCompat$1.class", "size": 219, "crc": 643493008}, {"key": "androidx/webkit/WebViewCompat$ExperimentalAsyncStartUp.class", "name": "androidx/webkit/WebViewCompat$ExperimentalAsyncStartUp.class", "size": 783, "crc": 478014774}, {"key": "androidx/webkit/WebViewCompat$ExperimentalCacheProvider.class", "name": "androidx/webkit/WebViewCompat$ExperimentalCacheProvider.class", "size": 785, "crc": -61491677}, {"key": "androidx/webkit/WebViewCompat$ExperimentalSaveState.class", "name": "androidx/webkit/WebViewCompat$ExperimentalSaveState.class", "size": 777, "crc": 234919426}, {"key": "androidx/webkit/WebViewCompat$ExperimentalUrlPrerender.class", "name": "androidx/webkit/WebViewCompat$ExperimentalUrlPrerender.class", "size": 783, "crc": 232936092}, {"key": "androidx/webkit/WebViewCompat$NullReturningWebViewStartUpResult.class", "name": "androidx/webkit/WebViewCompat$NullReturningWebViewStartUpResult.class", "size": 1108, "crc": -**********}, {"key": "androidx/webkit/WebViewCompat$VisualStateCallback.class", "name": "androidx/webkit/WebViewCompat$VisualStateCallback.class", "size": 334, "crc": **********}, {"key": "androidx/webkit/WebViewCompat$WebMessageListener.class", "name": "androidx/webkit/WebViewCompat$WebMessageListener.class", "size": 553, "crc": **********}, {"key": "androidx/webkit/WebViewCompat$WebViewStartUpCallback.class", "name": "androidx/webkit/WebViewCompat$WebViewStartUpCallback.class", "size": 581, "crc": -1684623328}, {"key": "androidx/webkit/WebViewCompat.class", "name": "androidx/webkit/WebViewCompat.class", "size": 25709, "crc": 660996395}, {"key": "androidx/webkit/WebViewFeature$WebViewStartupFeature.class", "name": "androidx/webkit/WebViewFeature$WebViewStartupFeature.class", "size": 758, "crc": 539797687}, {"key": "androidx/webkit/WebViewFeature$WebViewSupportFeature.class", "name": "androidx/webkit/WebViewFeature$WebViewSupportFeature.class", "size": 758, "crc": -750036063}, {"key": "androidx/webkit/WebViewFeature.class", "name": "androidx/webkit/WebViewFeature.class", "size": 5659, "crc": -1864652236}, {"key": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig$Builder.class", "name": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig$Builder.class", "size": 2395, "crc": -1021787706}, {"key": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig.class", "name": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig.class", "size": 1792, "crc": 836344649}, {"key": "androidx/webkit/WebViewRenderProcess.class", "name": "androidx/webkit/WebViewRenderProcess.class", "size": 347, "crc": -253149553}, {"key": "androidx/webkit/WebViewRenderProcessClient.class", "name": "androidx/webkit/WebViewRenderProcessClient.class", "size": 628, "crc": -800595558}, {"key": "androidx/webkit/WebViewStartUpConfig$1.class", "name": "androidx/webkit/WebViewStartUpConfig$1.class", "size": 240, "crc": -1409176776}, {"key": "androidx/webkit/WebViewStartUpConfig$Builder.class", "name": "androidx/webkit/WebViewStartUpConfig$Builder.class", "size": 1418, "crc": -647415117}, {"key": "androidx/webkit/WebViewStartUpConfig.class", "name": "androidx/webkit/WebViewStartUpConfig.class", "size": 1458, "crc": -2016396734}, {"key": "androidx/webkit/WebViewStartUpResult.class", "name": "androidx/webkit/WebViewStartUpResult.class", "size": 821, "crc": 1984489719}, {"key": "androidx/webkit/internal/ApiFeature$LAZY_HOLDER.class", "name": "androidx/webkit/internal/ApiFeature$LAZY_HOLDER.class", "size": 992, "crc": -1970796652}, {"key": "androidx/webkit/internal/ApiFeature$M.class", "name": "androidx/webkit/internal/ApiFeature$M.class", "size": 863, "crc": -1845953493}, {"key": "androidx/webkit/internal/ApiFeature$N.class", "name": "androidx/webkit/internal/ApiFeature$N.class", "size": 863, "crc": -1322000141}, {"key": "androidx/webkit/internal/ApiFeature$NoFramework.class", "name": "androidx/webkit/internal/ApiFeature$NoFramework.class", "size": 700, "crc": -2069954118}, {"key": "androidx/webkit/internal/ApiFeature$O.class", "name": "androidx/webkit/internal/ApiFeature$O.class", "size": 863, "crc": -2032696098}, {"key": "androidx/webkit/internal/ApiFeature$O_MR1.class", "name": "androidx/webkit/internal/ApiFeature$O_MR1.class", "size": 875, "crc": -683944852}, {"key": "androidx/webkit/internal/ApiFeature$P.class", "name": "androidx/webkit/internal/ApiFeature$P.class", "size": 863, "crc": 805007742}, {"key": "androidx/webkit/internal/ApiFeature$Q.class", "name": "androidx/webkit/internal/ApiFeature$Q.class", "size": 863, "crc": -273781511}, {"key": "androidx/webkit/internal/ApiFeature$T.class", "name": "androidx/webkit/internal/ApiFeature$T.class", "size": 863, "crc": -399723672}, {"key": "androidx/webkit/internal/ApiFeature.class", "name": "androidx/webkit/internal/ApiFeature.class", "size": 2577, "crc": 735280359}, {"key": "androidx/webkit/internal/ApiHelperForM$1.class", "name": "androidx/webkit/internal/ApiHelperForM$1.class", "size": 1515, "crc": 325611959}, {"key": "androidx/webkit/internal/ApiHelperForM$2.class", "name": "androidx/webkit/internal/ApiHelperForM$2.class", "size": 1535, "crc": 328056812}, {"key": "androidx/webkit/internal/ApiHelperForM$3.class", "name": "androidx/webkit/internal/ApiHelperForM$3.class", "size": 984, "crc": -142444172}, {"key": "androidx/webkit/internal/ApiHelperForM.class", "name": "androidx/webkit/internal/ApiHelperForM.class", "size": 5287, "crc": -831765341}, {"key": "androidx/webkit/internal/ApiHelperForN.class", "name": "androidx/webkit/internal/ApiHelperForN.class", "size": 4345, "crc": -698946243}, {"key": "androidx/webkit/internal/ApiHelperForO.class", "name": "androidx/webkit/internal/ApiHelperForO.class", "size": 1532, "crc": 420825271}, {"key": "androidx/webkit/internal/ApiHelperForOMR1.class", "name": "androidx/webkit/internal/ApiHelperForOMR1.class", "size": 2055, "crc": -1794937422}, {"key": "androidx/webkit/internal/ApiHelperForP.class", "name": "androidx/webkit/internal/ApiHelperForP.class", "size": 2735, "crc": -1432235606}, {"key": "androidx/webkit/internal/ApiHelperForQ.class", "name": "androidx/webkit/internal/ApiHelperForQ.class", "size": 2801, "crc": -729327596}, {"key": "androidx/webkit/internal/ApiHelperForTiramisu.class", "name": "androidx/webkit/internal/ApiHelperForTiramisu.class", "size": 1395, "crc": -1859068649}, {"key": "androidx/webkit/internal/AssetHelper.class", "name": "androidx/webkit/internal/AssetHelper.class", "size": 5563, "crc": -1419832259}, {"key": "androidx/webkit/internal/ConditionallySupportedFeature.class", "name": "androidx/webkit/internal/ConditionallySupportedFeature.class", "size": 329, "crc": -896488899}, {"key": "androidx/webkit/internal/CookieManagerAdapter.class", "name": "androidx/webkit/internal/CookieManagerAdapter.class", "size": 1002, "crc": -674795445}, {"key": "androidx/webkit/internal/FrameworkServiceWorkerClient.class", "name": "androidx/webkit/internal/FrameworkServiceWorkerClient.class", "size": 1048, "crc": -994022055}, {"key": "androidx/webkit/internal/IncompatibleApkWebViewProviderFactory.class", "name": "androidx/webkit/internal/IncompatibleApkWebViewProviderFactory.class", "size": 3343, "crc": **********}, {"key": "androidx/webkit/internal/JavaScriptReplyProxyImpl.class", "name": "androidx/webkit/internal/JavaScriptReplyProxyImpl.class", "size": 3405, "crc": -879086887}, {"key": "androidx/webkit/internal/MimeUtil.class", "name": "androidx/webkit/internal/MimeUtil.class", "size": 3763, "crc": **********}, {"key": "androidx/webkit/internal/NavigationAdapter.class", "name": "androidx/webkit/internal/NavigationAdapter.class", "size": 2473, "crc": -**********}, {"key": "androidx/webkit/internal/NoVarySearchHeaderAdapter.class", "name": "androidx/webkit/internal/NoVarySearchHeaderAdapter.class", "size": 1359, "crc": **********}, {"key": "androidx/webkit/internal/PageImpl.class", "name": "androidx/webkit/internal/PageImpl.class", "size": 621, "crc": -**********}, {"key": "androidx/webkit/internal/PrefetchOperationCallbackAdapter$1.class", "name": "androidx/webkit/internal/PrefetchOperationCallbackAdapter$1.class", "size": 1730, "crc": 439429568}, {"key": "androidx/webkit/internal/PrefetchOperationCallbackAdapter.class", "name": "androidx/webkit/internal/PrefetchOperationCallbackAdapter.class", "size": 1479, "crc": 301630215}, {"key": "androidx/webkit/internal/ProfileImpl.class", "name": "androidx/webkit/internal/ProfileImpl.class", "size": 5990, "crc": -580418896}, {"key": "androidx/webkit/internal/ProfileStoreImpl.class", "name": "androidx/webkit/internal/ProfileStoreImpl.class", "size": 3292, "crc": 808514257}, {"key": "androidx/webkit/internal/ProxyControllerImpl.class", "name": "androidx/webkit/internal/ProxyControllerImpl.class", "size": 3827, "crc": 1156247285}, {"key": "androidx/webkit/internal/SafeBrowsingResponseImpl.class", "name": "androidx/webkit/internal/SafeBrowsingResponseImpl.class", "size": 3512, "crc": 175178014}, {"key": "androidx/webkit/internal/ScriptHandlerImpl.class", "name": "androidx/webkit/internal/ScriptHandlerImpl.class", "size": 1301, "crc": -1642504527}, {"key": "androidx/webkit/internal/ServiceWorkerClientAdapter.class", "name": "androidx/webkit/internal/ServiceWorkerClientAdapter.class", "size": 1257, "crc": -1626777409}, {"key": "androidx/webkit/internal/ServiceWorkerControllerImpl.class", "name": "androidx/webkit/internal/ServiceWorkerControllerImpl.class", "size": 3569, "crc": 1810871567}, {"key": "androidx/webkit/internal/ServiceWorkerWebSettingsImpl.class", "name": "androidx/webkit/internal/ServiceWorkerWebSettingsImpl.class", "size": 5494, "crc": -1493206991}, {"key": "androidx/webkit/internal/SpeculativeLoadingConfigAdapter.class", "name": "androidx/webkit/internal/SpeculativeLoadingConfigAdapter.class", "size": 1066, "crc": 1372617484}, {"key": "androidx/webkit/internal/SpeculativeLoadingParametersAdapter.class", "name": "androidx/webkit/internal/SpeculativeLoadingParametersAdapter.class", "size": 1899, "crc": -1390470531}, {"key": "androidx/webkit/internal/StartupApiFeature$NoFramework.class", "name": "androidx/webkit/internal/StartupApiFeature$NoFramework.class", "size": 728, "crc": -1378283267}, {"key": "androidx/webkit/internal/StartupApiFeature$P.class", "name": "androidx/webkit/internal/StartupApiFeature$P.class", "size": 891, "crc": 2076462815}, {"key": "androidx/webkit/internal/StartupApiFeature.class", "name": "androidx/webkit/internal/StartupApiFeature.class", "size": 4552, "crc": 1359619794}, {"key": "androidx/webkit/internal/StartupFeatures.class", "name": "androidx/webkit/internal/StartupFeatures.class", "size": 552, "crc": -80103831}, {"key": "androidx/webkit/internal/TracingControllerImpl.class", "name": "androidx/webkit/internal/TracingControllerImpl.class", "size": 3435, "crc": 121866650}, {"key": "androidx/webkit/internal/UserAgentMetadataInternal.class", "name": "androidx/webkit/internal/UserAgentMetadataInternal.class", "size": 5178, "crc": -213874847}, {"key": "androidx/webkit/internal/VisualStateCallbackAdapter.class", "name": "androidx/webkit/internal/VisualStateCallbackAdapter.class", "size": 982, "crc": -413467183}, {"key": "androidx/webkit/internal/WebMessageAdapter.class", "name": "androidx/webkit/internal/WebMessageAdapter.class", "size": 4732, "crc": -1666283494}, {"key": "androidx/webkit/internal/WebMessageCallbackAdapter.class", "name": "androidx/webkit/internal/WebMessageCallbackAdapter.class", "size": 2181, "crc": 1900890273}, {"key": "androidx/webkit/internal/WebMessageListenerAdapter.class", "name": "androidx/webkit/internal/WebMessageListenerAdapter.class", "size": 2566, "crc": 125652778}, {"key": "androidx/webkit/internal/WebMessagePayloadAdapter.class", "name": "androidx/webkit/internal/WebMessagePayloadAdapter.class", "size": 2078, "crc": 1753062989}, {"key": "androidx/webkit/internal/WebMessagePortImpl.class", "name": "androidx/webkit/internal/WebMessagePortImpl.class", "size": 6240, "crc": 548110665}, {"key": "androidx/webkit/internal/WebNavigationClientAdapter.class", "name": "androidx/webkit/internal/WebNavigationClientAdapter.class", "size": 5257, "crc": -1495940830}, {"key": "androidx/webkit/internal/WebResourceErrorImpl.class", "name": "androidx/webkit/internal/WebResourceErrorImpl.class", "size": 3209, "crc": -525461465}, {"key": "androidx/webkit/internal/WebResourceRequestAdapter.class", "name": "androidx/webkit/internal/WebResourceRequestAdapter.class", "size": 837, "crc": -44677346}, {"key": "androidx/webkit/internal/WebSettingsAdapter.class", "name": "androidx/webkit/internal/WebSettingsAdapter.class", "size": 6236, "crc": -814795986}, {"key": "androidx/webkit/internal/WebSettingsNoOpAdapter.class", "name": "androidx/webkit/internal/WebSettingsNoOpAdapter.class", "size": 4839, "crc": 519857113}, {"key": "androidx/webkit/internal/WebStorageAdapter.class", "name": "androidx/webkit/internal/WebStorageAdapter.class", "size": 1276, "crc": -1917518047}, {"key": "androidx/webkit/internal/WebViewFeatureInternal$1.class", "name": "androidx/webkit/internal/WebViewFeatureInternal$1.class", "size": 1832, "crc": -1980215407}, {"key": "androidx/webkit/internal/WebViewFeatureInternal$2.class", "name": "androidx/webkit/internal/WebViewFeatureInternal$2.class", "size": 1013, "crc": -1626071677}, {"key": "androidx/webkit/internal/WebViewFeatureInternal$3.class", "name": "androidx/webkit/internal/WebViewFeatureInternal$3.class", "size": 927, "crc": 1707598547}, {"key": "androidx/webkit/internal/WebViewFeatureInternal.class", "name": "androidx/webkit/internal/WebViewFeatureInternal.class", "size": 11071, "crc": -1113233724}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_COMPAT_CONVERTER_HOLDER.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_COMPAT_CONVERTER_HOLDER.class", "size": 1072, "crc": -251944777}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_FACTORY_HOLDER.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_FACTORY_HOLDER.class", "size": 724, "crc": 87249189}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator.class", "size": 3952, "crc": **********}, {"key": "androidx/webkit/internal/WebViewProviderAdapter.class", "name": "androidx/webkit/internal/WebViewProviderAdapter.class", "size": 10943, "crc": -**********}, {"key": "androidx/webkit/internal/WebViewProviderFactory.class", "name": "androidx/webkit/internal/WebViewProviderFactory.class", "size": 1671, "crc": **********}, {"key": "androidx/webkit/internal/WebViewProviderFactoryAdapter.class", "name": "androidx/webkit/internal/WebViewProviderFactoryAdapter.class", "size": 4761, "crc": -54352709}, {"key": "androidx/webkit/internal/WebViewRenderProcessClientAdapter.class", "name": "androidx/webkit/internal/WebViewRenderProcessClientAdapter.class", "size": 3692, "crc": -322744123}, {"key": "androidx/webkit/internal/WebViewRenderProcessClientFrameworkAdapter.class", "name": "androidx/webkit/internal/WebViewRenderProcessClientFrameworkAdapter.class", "size": 1750, "crc": **********}, {"key": "androidx/webkit/internal/WebViewRenderProcessImpl.class", "name": "androidx/webkit/internal/WebViewRenderProcessImpl.class", "size": 4063, "crc": -**********}, {"key": "androidx/webkit/internal/WebViewStartUpCallbackAdapter$1.class", "name": "androidx/webkit/internal/WebViewStartUpCallbackAdapter$1.class", "size": 2731, "crc": 652808148}, {"key": "androidx/webkit/internal/WebViewStartUpCallbackAdapter$BlockingStartUpLocationImpl.class", "name": "androidx/webkit/internal/WebViewStartUpCallbackAdapter$BlockingStartUpLocationImpl.class", "size": 1061, "crc": -1132751777}, {"key": "androidx/webkit/internal/WebViewStartUpCallbackAdapter.class", "name": "androidx/webkit/internal/WebViewStartUpCallbackAdapter.class", "size": 2274, "crc": -556719791}, {"key": "androidx/webkit/internal/WebViewStartUpConfigAdapter.class", "name": "androidx/webkit/internal/WebViewStartUpConfigAdapter.class", "size": 1022, "crc": **********}, {"key": "androidx/webkit/internal/WebkitToCompatConverter.class", "name": "androidx/webkit/internal/WebkitToCompatConverter.class", "size": 5338, "crc": -**********}, {"key": "androidx/webkit/internal/package-info.class", "name": "androidx/webkit/internal/package-info.class", "size": 393, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/DropDataContentProviderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/DropDataContentProviderBoundaryInterface.class", "size": 1170, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/FeatureFlagHolderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/FeatureFlagHolderBoundaryInterface.class", "size": 318, "crc": **********}, {"key": "org/chromium/support_lib_boundary/IsomorphicObjectBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/IsomorphicObjectBoundaryInterface.class", "size": 519, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/JsReplyProxyBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/JsReplyProxyBoundaryInterface.class", "size": 535, "crc": **********}, {"key": "org/chromium/support_lib_boundary/NoVarySearchDataBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/NoVarySearchDataBoundaryInterface.class", "size": 503, "crc": 641234702}, {"key": "org/chromium/support_lib_boundary/PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface.class", "size": 686, "crc": -1542974981}, {"key": "org/chromium/support_lib_boundary/PrefetchOperationCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/PrefetchOperationCallbackBoundaryInterface.class", "size": 544, "crc": 387497503}, {"key": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey.class", "name": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey.class", "size": 844, "crc": -1803596267}, {"key": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants.class", "name": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants.class", "size": 834, "crc": -1829362991}, {"key": "org/chromium/support_lib_boundary/ProfileBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProfileBoundaryInterface.class", "size": 1174, "crc": -1446513462}, {"key": "org/chromium/support_lib_boundary/ProfileStoreBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProfileStoreBoundaryInterface.class", "size": 606, "crc": -1708488974}, {"key": "org/chromium/support_lib_boundary/ProxyControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProxyControllerBoundaryInterface.class", "size": 573, "crc": -206875858}, {"key": "org/chromium/support_lib_boundary/SafeBrowsingResponseBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/SafeBrowsingResponseBoundaryInterface.class", "size": 344, "crc": -1257524104}, {"key": "org/chromium/support_lib_boundary/ScriptHandlerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ScriptHandlerBoundaryInterface.class", "size": 278, "crc": 555719236}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerClientBoundaryInterface.class", "size": 536, "crc": 960803826}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerControllerBoundaryInterface.class", "size": 516, "crc": -1311120841}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerWebSettingsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerWebSettingsBoundaryInterface.class", "size": 857, "crc": -582142268}, {"key": "org/chromium/support_lib_boundary/SpeculativeLoadingConfigBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/SpeculativeLoadingConfigBoundaryInterface.class", "size": 369, "crc": 303842781}, {"key": "org/chromium/support_lib_boundary/SpeculativeLoadingParametersBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/SpeculativeLoadingParametersBoundaryInterface.class", "size": 606, "crc": 911168770}, {"key": "org/chromium/support_lib_boundary/StaticsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/StaticsBoundaryInterface.class", "size": 1141, "crc": 1080633882}, {"key": "org/chromium/support_lib_boundary/TracingControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/TracingControllerBoundaryInterface.class", "size": 581, "crc": 1414541241}, {"key": "org/chromium/support_lib_boundary/VisualStateCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/VisualStateCallbackBoundaryInterface.class", "size": 295, "crc": -609692756}, {"key": "org/chromium/support_lib_boundary/WebMessageBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageBoundaryInterface.class", "size": 652, "crc": -2040261299}, {"key": "org/chromium/support_lib_boundary/WebMessageCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageCallbackBoundaryInterface.class", "size": 441, "crc": 783274838}, {"key": "org/chromium/support_lib_boundary/WebMessageListenerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageListenerBoundaryInterface.class", "size": 487, "crc": -388320856}, {"key": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface$WebMessagePayloadType.class", "name": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface$WebMessagePayloadType.class", "size": 609, "crc": -1633319233}, {"key": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface.class", "size": 677, "crc": 64071006}, {"key": "org/chromium/support_lib_boundary/WebMessagePortBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessagePortBoundaryInterface.class", "size": 532, "crc": -1353376540}, {"key": "org/chromium/support_lib_boundary/WebResourceErrorBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebResourceErrorBoundaryInterface.class", "size": 344, "crc": -1306141108}, {"key": "org/chromium/support_lib_boundary/WebResourceRequestBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebResourceRequestBoundaryInterface.class", "size": 292, "crc": -652886419}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$AttributionBehavior.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$AttributionBehavior.class", "size": 693, "crc": -794881792}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$ForceDarkBehavior.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$ForceDarkBehavior.class", "size": 644, "crc": 194043176}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$SpeculativeLoadingStatus.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$SpeculativeLoadingStatus.class", "size": 594, "crc": 79341145}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus.class", "size": 742, "crc": 416505625}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebauthnSupport.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebauthnSupport.class", "size": 589, "crc": -817066326}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface.class", "size": 2799, "crc": -374035914}, {"key": "org/chromium/support_lib_boundary/WebStorageBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebStorageBoundaryInterface.class", "size": 463, "crc": -1569378453}, {"key": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$Baseline.class", "name": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$Baseline.class", "size": 529, "crc": 1442754865}, {"key": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$Config.class", "name": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$Config.class", "size": 2502, "crc": -368537700}, {"key": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$ConfigField.class", "name": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface$ConfigField.class", "size": 674, "crc": -1978454880}, {"key": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewBuilderBoundaryInterface.class", "size": 853, "crc": 810387781}, {"key": "org/chromium/support_lib_boundary/WebViewClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewClientBoundaryInterface.class", "size": 899, "crc": -675062038}, {"key": "org/chromium/support_lib_boundary/WebViewCookieManagerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewCookieManagerBoundaryInterface.class", "size": 411, "crc": 2086703917}, {"key": "org/chromium/support_lib_boundary/WebViewNavigationBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewNavigationBoundaryInterface.class", "size": 757, "crc": 38115677}, {"key": "org/chromium/support_lib_boundary/WebViewNavigationClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewNavigationClientBoundaryInterface.class", "size": 540, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebViewPageBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewPageBoundaryInterface.class", "size": 326, "crc": 766890077}, {"key": "org/chromium/support_lib_boundary/WebViewProviderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewProviderBoundaryInterface.class", "size": 2302, "crc": 639122458}, {"key": "org/chromium/support_lib_boundary/WebViewProviderFactoryBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewProviderFactoryBoundaryInterface.class", "size": 970, "crc": 957045824}, {"key": "org/chromium/support_lib_boundary/WebViewRendererBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewRendererBoundaryInterface.class", "size": 360, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebViewRendererClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewRendererClientBoundaryInterface.class", "size": 478, "crc": **********}, {"key": "org/chromium/support_lib_boundary/WebViewStartUpCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewStartUpCallbackBoundaryInterface.class", "size": 336, "crc": -701445828}, {"key": "org/chromium/support_lib_boundary/WebViewStartUpConfigBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewStartUpConfigBoundaryInterface.class", "size": 383, "crc": 241289383}, {"key": "org/chromium/support_lib_boundary/WebViewStartUpResultBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewStartUpResultBoundaryInterface.class", "size": 496, "crc": 1507332555}, {"key": "org/chromium/support_lib_boundary/WebkitToCompatConverterBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebkitToCompatConverterBoundaryInterface.class", "size": 838, "crc": -728563126}, {"key": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil$InvocationHandlerWithDelegateGetter.class", "name": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil$InvocationHandlerWithDelegateGetter.class", "size": 2316, "crc": 547004370}, {"key": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil.class", "name": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil.class", "size": 5434, "crc": 1696098270}, {"key": "org/chromium/support_lib_boundary/util/Contract.class", "name": "org/chromium/support_lib_boundary/util/Contract.class", "size": 437, "crc": -648329429}, {"key": "org/chromium/support_lib_boundary/util/Features.class", "name": "org/chromium/support_lib_boundary/util/Features.class", "size": 4146, "crc": 1535630076}, {"key": "META-INF/androidx.webkit_webkit.version", "name": "META-INF/androidx.webkit_webkit.version", "size": 7, "crc": 1083596569}]