<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽手柄 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:alpha="0.4" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 图标 -->
        <ImageView
            android:id="@+id/iv_alert_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_warning"
            android:tint="@color/primary"
            android:contentDescription="警告图标" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_alert_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="提示"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="@color/text_secondary"
            android:contentDescription="关闭"
            android:padding="8dp" />

    </LinearLayout>

    <!-- 消息内容卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/surface_variant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 消息内容 -->
            <TextView
                android:id="@+id/tv_alert_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="这里显示alert消息内容"
                android:textSize="16sp"
                android:textColor="@color/on_surface_variant"
                android:lineSpacingExtra="4dp"
                android:gravity="start" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <!-- 取消按钮 (可选，用于confirm类型) -->
        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:minHeight="56dp"
            android:text="取消"
            android:textSize="16sp"
            android:visibility="gone"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- 确定按钮 -->
        <Button
            android:id="@+id/btn_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="56dp"
            android:text="确定"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button"
            app:icon="@drawable/ic_check"
            app:iconGravity="start" />

    </LinearLayout>

</LinearLayout>
