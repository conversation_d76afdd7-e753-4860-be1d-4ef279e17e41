[{"key": "androidx/compose/animation/AndroidActualDefaultDecayAnimationSpec_androidKt.class", "name": "androidx/compose/animation/AndroidActualDefaultDecayAnimationSpec_androidKt.class", "size": 2069, "crc": -1457707851}, {"key": "androidx/compose/animation/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/animation/AndroidFlingSpline$FlingResult.class", "size": 2811, "crc": -1944528962}, {"key": "androidx/compose/animation/AndroidFlingSpline.class", "name": "androidx/compose/animation/AndroidFlingSpline.class", "size": 2296, "crc": -329558283}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$1.class", "size": 3067, "crc": -1357623482}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$2.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$2.class", "size": 1238, "crc": 745208684}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$3.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$3.class", "size": 3400, "crc": 1343811369}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$4.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$4.class", "size": 3075, "crc": 1348120116}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$5.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$5.class", "size": 1246, "crc": 1013482294}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1$1.class", "size": 2244, "crc": -1687632887}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1.class", "size": 2925, "crc": 582244657}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$3$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$3$1.class", "size": 1609, "crc": -767058369}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$4$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$4$1.class", "size": 2192, "crc": -766896649}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1$invoke$$inlined$onDispose$1.class", "size": 2810, "crc": 1702184333}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1.class", "size": 3588, "crc": -1915657944}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5.class", "size": 7934, "crc": 496097933}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1.class", "size": 11985, "crc": -252757717}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$9.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$9.class", "size": 3490, "crc": -1422460747}, {"key": "androidx/compose/animation/AnimatedContentKt$SizeTransform$1.class", "name": "androidx/compose/animation/AnimatedContentKt$SizeTransform$1.class", "size": 2440, "crc": 930401066}, {"key": "androidx/compose/animation/AnimatedContentKt.class", "name": "androidx/compose/animation/AnimatedContentKt.class", "size": 24738, "crc": 1197368125}, {"key": "androidx/compose/animation/AnimatedContentMeasurePolicy$measure$3.class", "name": "androidx/compose/animation/AnimatedContentMeasurePolicy$measure$3.class", "size": 4278, "crc": 820302835}, {"key": "androidx/compose/animation/AnimatedContentMeasurePolicy.class", "name": "androidx/compose/animation/AnimatedContentMeasurePolicy.class", "size": 11763, "crc": -1277172277}, {"key": "androidx/compose/animation/AnimatedContentScope.class", "name": "androidx/compose/animation/AnimatedContentScope.class", "size": 593, "crc": -1664386784}, {"key": "androidx/compose/animation/AnimatedContentScopeImpl.class", "name": "androidx/compose/animation/AnimatedContentScopeImpl.class", "size": 2110, "crc": -1488662973}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection$Companion.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection$Companion.class", "size": 2047, "crc": -942500977}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection.class", "size": 3355, "crc": 528007307}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$slideIntoContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$slideIntoContainer$1.class", "size": 1803, "crc": -1581767346}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$slideOutOfContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$slideOutOfContainer$1.class", "size": 1805, "crc": -448661785}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope.class", "size": 6026, "crc": 629083437}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$ChildData.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$ChildData.class", "size": 3486, "crc": -114131844}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$1.class", "size": 3042, "crc": -1561992032}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$size$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$size$1.class", "size": 4168, "crc": -687387992}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$size$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier$measure$size$2.class", "size": 2445, "crc": 1279769988}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifier.class", "size": 5669, "crc": 1832554266}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$1.class", "size": 2810, "crc": 1330557736}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$2.class", "size": 2730, "crc": -1479219341}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$3.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$3.class", "size": 2811, "crc": 224218467}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$4.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$4.class", "size": 2730, "crc": 1290216273}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$1.class", "size": 3342, "crc": 1426430406}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$2.class", "size": 3379, "crc": -1705906383}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$3.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$3.class", "size": 3342, "crc": 1942130890}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$4.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$4.class", "size": 3380, "crc": -775123260}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl.class", "size": 19510, "crc": -2007746572}, {"key": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy$measure$1.class", "name": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy$measure$1.class", "size": 3330, "crc": -16999169}, {"key": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy.class", "name": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy.class", "size": 10051, "crc": -1570479696}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1$1$1.class", "size": 2044, "crc": -516170306}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1.class", "size": 3637, "crc": 378919183}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$4.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$4.class", "size": 4045, "crc": -2095090981}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$1.class", "size": 1865, "crc": -621496381}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$2.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$2.class", "size": 3708, "crc": 1661856842}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1.class", "size": 5547, "crc": -1045801203}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$1.class", "size": 1623, "crc": 2028515935}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$10.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$10.class", "size": 3536, "crc": 1811542812}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$11.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$11.class", "size": 1728, "crc": 625602882}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$12.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$12.class", "size": 3548, "crc": -1090122322}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$13.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$13.class", "size": 3357, "crc": -1525815625}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$2.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$2.class", "size": 2940, "crc": 133216486}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$3.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$3.class", "size": 1668, "crc": -408603614}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$4.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$4.class", "size": 3200, "crc": -775099898}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$5.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$5.class", "size": 1671, "crc": 219992294}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$6.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$6.class", "size": 3212, "crc": -573737488}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$7.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$7.class", "size": 1678, "crc": 300056013}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$8.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$8.class", "size": 3274, "crc": 586144842}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$9.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$9.class", "size": 1723, "crc": 634656103}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1$1.class", "size": 2032, "crc": 1206523433}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1.class", "size": 5109, "crc": -2057024939}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$2.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$2.class", "size": 2142, "crc": -726559606}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$3.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$3.class", "size": 3289, "crc": -238194173}, {"key": "androidx/compose/animation/AnimatedVisibilityKt.class", "name": "androidx/compose/animation/AnimatedVisibilityKt.class", "size": 47291, "crc": -2070038063}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$DefaultImpls.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$DefaultImpls.class", "size": 1612, "crc": 992201375}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$$inlined$debugInspectorInfo$1.class", "size": 3343, "crc": 1503326762}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$2.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$2.class", "size": 3614, "crc": 2140326692}, {"key": "androidx/compose/animation/AnimatedVisibilityScope.class", "name": "androidx/compose/animation/AnimatedVisibilityScope.class", "size": 5029, "crc": 1734957784}, {"key": "androidx/compose/animation/AnimatedVisibilityScopeImpl.class", "name": "androidx/compose/animation/AnimatedVisibilityScopeImpl.class", "size": 2842, "crc": -2087095322}, {"key": "androidx/compose/animation/AnimationModifierKt.class", "name": "androidx/compose/animation/AnimationModifierKt.class", "size": 4814, "crc": 1448044749}, {"key": "androidx/compose/animation/BoundsAnimation$animate$1.class", "name": "androidx/compose/animation/BoundsAnimation$animate$1.class", "size": 2244, "crc": 1840261450}, {"key": "androidx/compose/animation/BoundsAnimation$animate$2.class", "name": "androidx/compose/animation/BoundsAnimation$animate$2.class", "size": 1998, "crc": -973216326}, {"key": "androidx/compose/animation/BoundsAnimation.class", "name": "androidx/compose/animation/BoundsAnimation.class", "size": 10082, "crc": 883419219}, {"key": "androidx/compose/animation/BoundsAnimationKt.class", "name": "androidx/compose/animation/BoundsAnimationKt.class", "size": 1458, "crc": 583610386}, {"key": "androidx/compose/animation/BoundsTransform.class", "name": "androidx/compose/animation/BoundsTransform.class", "size": 1146, "crc": 1855179649}, {"key": "androidx/compose/animation/ChangeSize$1.class", "name": "androidx/compose/animation/ChangeSize$1.class", "size": 1603, "crc": 1291241887}, {"key": "androidx/compose/animation/ChangeSize.class", "name": "androidx/compose/animation/ChangeSize.class", "size": 5788, "crc": -52918870}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$1.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$1.class", "size": 3285, "crc": 1181417801}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$2.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$2.class", "size": 4502, "crc": -1390886251}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1.class", "size": 2612, "crc": 1477228073}, {"key": "androidx/compose/animation/ColorVectorConverterKt.class", "name": "androidx/compose/animation/ColorVectorConverterKt.class", "size": 2246, "crc": 406388177}, {"key": "androidx/compose/animation/ContentScaleTransitionEffect$Key.class", "name": "androidx/compose/animation/ContentScaleTransitionEffect$Key.class", "size": 1187, "crc": -1792738508}, {"key": "androidx/compose/animation/ContentScaleTransitionEffect.class", "name": "androidx/compose/animation/ContentScaleTransitionEffect.class", "size": 3990, "crc": -849781997}, {"key": "androidx/compose/animation/ContentTransform.class", "name": "androidx/compose/animation/ContentTransform.class", "size": 4435, "crc": -1707194004}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$1.class", "size": 2754, "crc": 84336811}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$2.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$2.class", "size": 2632, "crc": -736797880}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$3.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$3.class", "size": 1194, "crc": 1375343770}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$4$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$4$1.class", "size": 2053, "crc": 1411600481}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$1$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$1$1.class", "size": 1854, "crc": 1760386101}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$alpha$2.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$alpha$2.class", "size": 3201, "crc": -892443485}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1.class", "size": 14791, "crc": 10809257}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$7.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$7.class", "size": 3112, "crc": -696579836}, {"key": "androidx/compose/animation/CrossfadeKt.class", "name": "androidx/compose/animation/CrossfadeKt.class", "size": 20879, "crc": -1590830839}, {"key": "androidx/compose/animation/EnterExitState.class", "name": "androidx/compose/animation/EnterExitState.class", "size": 1477, "crc": -2096191789}, {"key": "androidx/compose/animation/EnterExitTransitionElement.class", "name": "androidx/compose/animation/EnterExitTransitionElement.class", "size": 14612, "crc": -1563754649}, {"key": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$1.class", "size": 1798, "crc": 1292026211}, {"key": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$2.class", "size": 1879, "crc": -2104029321}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$1.class", "size": 3490, "crc": 316984504}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2$WhenMappings.class", "size": 970, "crc": -1975902086}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2.class", "size": 2833, "crc": 1503176815}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$block$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$block$1.class", "size": 3199, "crc": 912280324}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$1.class", "size": 3493, "crc": 688520533}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2$WhenMappings.class", "size": 970, "crc": 1874278264}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2.class", "size": 2825, "crc": 507316786}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$1.class", "size": 2739, "crc": -984713586}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2$WhenMappings.class", "size": 990, "crc": -569864050}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2.class", "size": 3386, "crc": 242980389}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createModifier$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createModifier$1.class", "size": 1454, "crc": -754854234}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createModifier$2$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createModifier$2$1.class", "size": 2259, "crc": 1505054105}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$1.class", "size": 1740, "crc": 1868182613}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$2.class", "size": 2214, "crc": 2065967388}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandIn$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandIn$1.class", "size": 1688, "crc": 866491426}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$1.class", "size": 1728, "crc": 796653968}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$2.class", "size": 2203, "crc": -613781045}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$1.class", "size": 1739, "crc": 245528367}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$2.class", "size": 2212, "crc": 54418955}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkOut$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkOut$1.class", "size": 1690, "crc": 1225604758}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$1.class", "size": 1727, "crc": -389178397}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$2.class", "size": 2201, "crc": 310123273}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$1.class", "size": 1601, "crc": -356442245}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$2.class", "size": 2141, "crc": 1087813137}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$1.class", "size": 1595, "crc": -2061216876}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$2.class", "size": 2136, "crc": 878215224}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$1.class", "size": 1603, "crc": -1507661342}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$2.class", "size": 2142, "crc": -233939479}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$1.class", "size": 1597, "crc": 642704452}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$2.class", "size": 2137, "crc": 1456023958}, {"key": "androidx/compose/animation/EnterExitTransitionKt.class", "name": "androidx/compose/animation/EnterExitTransitionKt.class", "size": 47601, "crc": -2009857091}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$WhenMappings.class", "size": 908, "crc": -1869071713}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$1.class", "size": 1977, "crc": 421970189}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$2.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$2.class", "size": 2538, "crc": -281196774}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$3$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$3$1.class", "size": 1992, "crc": -1405758135}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$animSize$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$animSize$1.class", "size": 2043, "crc": -819299102}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$1.class", "size": 2539, "crc": -58033885}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$2.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$2.class", "size": 2065, "crc": -567446907}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$slideOffset$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$slideOffset$1.class", "size": 2069, "crc": -1570621758}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$sizeTransitionSpec$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$sizeTransitionSpec$1.class", "size": 3902, "crc": 668861962}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$slideSpec$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$slideSpec$1.class", "size": 3888, "crc": -511544760}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode.class", "size": 19123, "crc": 855570844}, {"key": "androidx/compose/animation/EnterTransition$Companion.class", "name": "androidx/compose/animation/EnterTransition$Companion.class", "size": 1168, "crc": -1177917286}, {"key": "androidx/compose/animation/EnterTransition.class", "name": "androidx/compose/animation/EnterTransition.class", "size": 4437, "crc": 1860301090}, {"key": "androidx/compose/animation/EnterTransitionImpl.class", "name": "androidx/compose/animation/EnterTransitionImpl.class", "size": 1185, "crc": 951201448}, {"key": "androidx/compose/animation/ExitTransition$Companion.class", "name": "androidx/compose/animation/ExitTransition$Companion.class", "size": 1403, "crc": -1353066813}, {"key": "androidx/compose/animation/ExitTransition.class", "name": "androidx/compose/animation/ExitTransition.class", "size": 5146, "crc": 969402190}, {"key": "androidx/compose/animation/ExitTransitionImpl.class", "name": "androidx/compose/animation/ExitTransitionImpl.class", "size": 1181, "crc": -50119027}, {"key": "androidx/compose/animation/ExperimentalAnimationApi.class", "name": "androidx/compose/animation/ExperimentalAnimationApi.class", "size": 1048, "crc": -864176298}, {"key": "androidx/compose/animation/ExperimentalSharedTransitionApi.class", "name": "androidx/compose/animation/ExperimentalSharedTransitionApi.class", "size": 1082, "crc": -209252529}, {"key": "androidx/compose/animation/Fade.class", "name": "androidx/compose/animation/Fade.class", "size": 3412, "crc": 472021772}, {"key": "androidx/compose/animation/FlingCalculator$FlingInfo.class", "name": "androidx/compose/animation/FlingCalculator$FlingInfo.class", "size": 4004, "crc": 517792807}, {"key": "androidx/compose/animation/FlingCalculator.class", "name": "androidx/compose/animation/FlingCalculator.class", "size": 2930, "crc": 2131007789}, {"key": "androidx/compose/animation/FlingCalculatorKt.class", "name": "androidx/compose/animation/FlingCalculatorKt.class", "size": 1082, "crc": 538844673}, {"key": "androidx/compose/animation/GraphicsLayerBlockForEnterExit.class", "name": "androidx/compose/animation/GraphicsLayerBlockForEnterExit.class", "size": 865, "crc": 758529937}, {"key": "androidx/compose/animation/LayerRenderer.class", "name": "androidx/compose/animation/LayerRenderer.class", "size": 1046, "crc": 126756468}, {"key": "androidx/compose/animation/LayoutModifierNodeWithPassThroughIntrinsics.class", "name": "androidx/compose/animation/LayoutModifierNodeWithPassThroughIntrinsics.class", "size": 2244, "crc": 584334427}, {"key": "androidx/compose/animation/LayoutModifierWithPassThroughIntrinsics.class", "name": "androidx/compose/animation/LayoutModifierWithPassThroughIntrinsics.class", "size": 2082, "crc": -517751378}, {"key": "androidx/compose/animation/OnLookaheadMeasured.class", "name": "androidx/compose/animation/OnLookaheadMeasured.class", "size": 577, "crc": 125870501}, {"key": "androidx/compose/animation/RemeasureImpl.class", "name": "androidx/compose/animation/RemeasureImpl.class", "size": 1006, "crc": -1754990629}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode$LayerWithRenderer.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode$LayerWithRenderer.class", "size": 8510, "crc": -1305544969}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode$draw$1.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode$draw$1.class", "size": 1756, "crc": 854509215}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode.class", "size": 9780, "crc": -1358064723}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNodeElement.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNodeElement.class", "size": 8398, "crc": 1318747604}, {"key": "androidx/compose/animation/Scale.class", "name": "androidx/compose/animation/Scale.class", "size": 4418, "crc": -392181509}, {"key": "androidx/compose/animation/ScaleToBoundsImpl.class", "name": "androidx/compose/animation/ScaleToBoundsImpl.class", "size": 1659, "crc": -1322225647}, {"key": "androidx/compose/animation/SharedBoundsNode$draw$1.class", "name": "androidx/compose/animation/SharedBoundsNode$draw$1.class", "size": 2011, "crc": 359088245}, {"key": "androidx/compose/animation/SharedBoundsNode$measure$1.class", "name": "androidx/compose/animation/SharedBoundsNode$measure$1.class", "size": 4009, "crc": 1712539681}, {"key": "androidx/compose/animation/SharedBoundsNode$onAttach$1.class", "name": "androidx/compose/animation/SharedBoundsNode$onAttach$1.class", "size": 1496, "crc": 519875731}, {"key": "androidx/compose/animation/SharedBoundsNode$onDetach$1.class", "name": "androidx/compose/animation/SharedBoundsNode$onDetach$1.class", "size": 1071, "crc": 73140774}, {"key": "androidx/compose/animation/SharedBoundsNode$place$1.class", "name": "androidx/compose/animation/SharedBoundsNode$place$1.class", "size": 5971, "crc": 1789698742}, {"key": "androidx/compose/animation/SharedBoundsNode$state$1.class", "name": "androidx/compose/animation/SharedBoundsNode$state$1.class", "size": 1563, "crc": 504735209}, {"key": "androidx/compose/animation/SharedBoundsNode.class", "name": "androidx/compose/animation/SharedBoundsNode.class", "size": 17054, "crc": 2030614638}, {"key": "androidx/compose/animation/SharedBoundsNodeElement.class", "name": "androidx/compose/animation/SharedBoundsNodeElement.class", "size": 4440, "crc": 1708099564}, {"key": "androidx/compose/animation/SharedContentNodeKt$ModifierLocalSharedElementInternalState$1.class", "name": "androidx/compose/animation/SharedContentNodeKt$ModifierLocalSharedElementInternalState$1.class", "size": 1360, "crc": -370384000}, {"key": "androidx/compose/animation/SharedContentNodeKt.class", "name": "androidx/compose/animation/SharedContentNodeKt.class", "size": 1593, "crc": 991480939}, {"key": "androidx/compose/animation/SharedElement$observingVisibilityChange$1.class", "name": "androidx/compose/animation/SharedElement$observingVisibilityChange$1.class", "size": 1366, "crc": 1220128455}, {"key": "androidx/compose/animation/SharedElement$updateMatch$1.class", "name": "androidx/compose/animation/SharedElement$updateMatch$1.class", "size": 1502, "crc": 755280871}, {"key": "androidx/compose/animation/SharedElement.class", "name": "androidx/compose/animation/SharedElement.class", "size": 12483, "crc": -693115562}, {"key": "androidx/compose/animation/SharedElementInternalState$lookaheadCoords$1.class", "name": "androidx/compose/animation/SharedElementInternalState$lookaheadCoords$1.class", "size": 1732, "crc": -1756112346}, {"key": "androidx/compose/animation/SharedElementInternalState.class", "name": "androidx/compose/animation/SharedElementInternalState.class", "size": 20223, "crc": -341128315}, {"key": "androidx/compose/animation/SharedTransitionScope$OverlayClip.class", "name": "androidx/compose/animation/SharedTransitionScope$OverlayClip.class", "size": 1464, "crc": -360669631}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$animatedSize$1.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$animatedSize$1.class", "size": 1213, "crc": -120224381}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$contentSize$1.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$contentSize$1.class", "size": 1210, "crc": -929917672}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion.class", "size": 1890, "crc": 763976958}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize.class", "size": 1122, "crc": 393398202}, {"key": "androidx/compose/animation/SharedTransitionScope$ResizeMode$Companion.class", "name": "androidx/compose/animation/SharedTransitionScope$ResizeMode$Companion.class", "size": 2893, "crc": 1128629583}, {"key": "androidx/compose/animation/SharedTransitionScope$ResizeMode.class", "name": "androidx/compose/animation/SharedTransitionScope$ResizeMode.class", "size": 1016, "crc": 637074820}, {"key": "androidx/compose/animation/SharedTransitionScope$SharedContentState.class", "name": "androidx/compose/animation/SharedTransitionScope$SharedContentState.class", "size": 4961, "crc": 224201639}, {"key": "androidx/compose/animation/SharedTransitionScope$renderInSharedTransitionScopeOverlay$1.class", "name": "androidx/compose/animation/SharedTransitionScope$renderInSharedTransitionScopeOverlay$1.class", "size": 1677, "crc": 505758332}, {"key": "androidx/compose/animation/SharedTransitionScope.class", "name": "androidx/compose/animation/SharedTransitionScope.class", "size": 13683, "crc": -350398894}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$ShapeBasedClip.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$ShapeBasedClip.class", "size": 3013, "crc": 1252545046}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$drawInOverlay$$inlined$sortBy$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$drawInOverlay$$inlined$sortBy$1.class", "size": 2786, "crc": -1287222600}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$observeAnimatingBlock$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$observeAnimatingBlock$1.class", "size": 4645, "crc": -1779872638}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$onStateRemoved$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$onStateRemoved$1$1.class", "size": 3990, "crc": -1071651131}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$1.class", "size": 2612, "crc": -591885231}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$1$1.class", "size": 1714, "crc": -212725809}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$2$1.class", "size": 1714, "crc": 417980384}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2.class", "size": 8483, "crc": 352699694}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsImpl$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsImpl$1.class", "size": 15290, "crc": -1850794616}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsWithCallerManagedVisibility$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsWithCallerManagedVisibility$1.class", "size": 2213, "crc": 754667133}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElement$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElement$1.class", "size": 2381, "crc": 292266929}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElementWithCallerManagedVisibility$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElementWithCallerManagedVisibility$1.class", "size": 2198, "crc": 282824709}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$updateTransitionActiveness$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$updateTransitionActiveness$1.class", "size": 1793, "crc": -254900703}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl.class", "size": 36027, "crc": 820170529}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$DefaultClipInOverlayDuringTransition$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$DefaultClipInOverlayDuringTransition$1.class", "size": 1663, "crc": -1477185872}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$DefaultEnabled$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$DefaultEnabled$1.class", "size": 1273, "crc": 305660246}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$ParentClip$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$ParentClip$1.class", "size": 2177, "crc": -1025761786}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$1.class", "size": 10361, "crc": 1263588858}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$2.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$2.class", "size": 2335, "crc": 1756423240}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionObserver$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionObserver$2$1.class", "size": 1727, "crc": -1331215129}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionObserver$2.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionObserver$2.class", "size": 2385, "crc": 759753088}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1$1.class", "size": 2970, "crc": -1037436987}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1.class", "size": 3058, "crc": 17481288}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$2$1.class", "size": 1946, "crc": -2054170825}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1$invoke$$inlined$onDispose$1.class", "size": 2492, "crc": 658717673}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1.class", "size": 3023, "crc": 1630691305}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1.class", "size": 9922, "crc": 1011680970}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$2.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$2.class", "size": 2168, "crc": -2120704269}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$createContentScaleModifier$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$createContentScaleModifier$1.class", "size": 2075, "crc": 393575385}, {"key": "androidx/compose/animation/SharedTransitionScopeKt.class", "name": "androidx/compose/animation/SharedTransitionScopeKt.class", "size": 15775, "crc": 373472905}, {"key": "androidx/compose/animation/SingleValueAnimationKt.class", "name": "androidx/compose/animation/SingleValueAnimationKt.class", "size": 7490, "crc": -1140211981}, {"key": "androidx/compose/animation/SizeAnimationModifierElement.class", "name": "androidx/compose/animation/SizeAnimationModifierElement.class", "size": 7343, "crc": -841456692}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$AnimData.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$AnimData.class", "size": 4640, "crc": 263461314}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$animateTo$data$1$1.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$animateTo$data$1$1.class", "size": 5145, "crc": 1600834047}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$measure$2.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$measure$2.class", "size": 2858, "crc": -1703234184}, {"key": "androidx/compose/animation/SizeAnimationModifierNode.class", "name": "androidx/compose/animation/SizeAnimationModifierNode.class", "size": 11630, "crc": -422063424}, {"key": "androidx/compose/animation/SizeTransform.class", "name": "androidx/compose/animation/SizeTransform.class", "size": 996, "crc": 74929338}, {"key": "androidx/compose/animation/SizeTransformImpl.class", "name": "androidx/compose/animation/SizeTransformImpl.class", "size": 3060, "crc": 1059086544}, {"key": "androidx/compose/animation/SkipToLookaheadElement.class", "name": "androidx/compose/animation/SkipToLookaheadElement.class", "size": 5846, "crc": -656913116}, {"key": "androidx/compose/animation/SkipToLookaheadNode$measure$1$1.class", "name": "androidx/compose/animation/SkipToLookaheadNode$measure$1$1.class", "size": 2027, "crc": -2087039213}, {"key": "androidx/compose/animation/SkipToLookaheadNode$measure$1.class", "name": "androidx/compose/animation/SkipToLookaheadNode$measure$1.class", "size": 5312, "crc": 1408918455}, {"key": "androidx/compose/animation/SkipToLookaheadNode.class", "name": "androidx/compose/animation/SkipToLookaheadNode.class", "size": 6624, "crc": 878667676}, {"key": "androidx/compose/animation/Slide.class", "name": "androidx/compose/animation/Slide.class", "size": 4344, "crc": -1924006970}, {"key": "androidx/compose/animation/SplineBasedDecayKt.class", "name": "androidx/compose/animation/SplineBasedDecayKt.class", "size": 2767, "crc": -669614808}, {"key": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec.class", "size": 2864, "crc": 334894249}, {"key": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec_androidKt.class", "name": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec_androidKt.class", "size": 5652, "crc": 613758906}, {"key": "androidx/compose/animation/TransitionData.class", "name": "androidx/compose/animation/TransitionData.class", "size": 6971, "crc": -2040121737}, {"key": "androidx/compose/animation/TransitionEffect.class", "name": "androidx/compose/animation/TransitionEffect.class", "size": 1301, "crc": 483929975}, {"key": "androidx/compose/animation/TransitionEffectKey.class", "name": "androidx/compose/animation/TransitionEffectKey.class", "size": 584, "crc": -796401661}, {"key": "androidx/compose/animation/TransitionKt$animateColor$1.class", "name": "androidx/compose/animation/TransitionKt$animateColor$1.class", "size": 3369, "crc": 1263574796}, {"key": "androidx/compose/animation/TransitionKt.class", "name": "androidx/compose/animation/TransitionKt.class", "size": 10267, "crc": 483600008}, {"key": "androidx/compose/animation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/animation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 564, "crc": 1388099600}, {"key": "META-INF/androidx.compose.animation_animation.version", "name": "META-INF/androidx.compose.animation_animation.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/animation_release.kotlin_module", "name": "META-INF/animation_release.kotlin_module", "size": 489, "crc": 2003478464}]