<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="8dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- 拖拽手柄 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bottom_sheet_handle"
        android:alpha="0.4" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <!-- 图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_accessibility"
            android:tint="@color/error"
            android:contentDescription="网络连接图标" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="网络连接异常"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 网络状态说明卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/error_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 问题描述标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="连接问题"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/on_error_container" />

            <!-- 问题描述 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="无法连接到Seeq服务器，请检查您的网络连接。"
                android:textSize="14sp"
                android:textColor="@color/on_error_container"
                android:lineSpacingExtra="2dp" />

            <!-- 可能原因列表 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="可能的原因："
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/on_error_container" />

            <!-- 原因1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="•"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="WiFi或移动网络未连接"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

            </LinearLayout>

            <!-- 原因2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="•"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="网络信号不稳定"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

            </LinearLayout>

            <!-- 原因3 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="•"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="防火墙或网络限制"
                    android:textSize="14sp"
                    android:textColor="@color/on_error_container" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 网络状态显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@drawable/info_background">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/ic_info"
            android:tint="@color/primary" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="当前网络状态"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_network_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="检测中..."
                android:textSize="14sp"
                android:textColor="@color/primary" />

        </LinearLayout>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 重试按钮 -->
        <Button
            android:id="@+id/btn_retry"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="重试连接"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button"
            app:icon="@drawable/ic_refresh"
            app:iconGravity="start" />

        <!-- 退出按钮 -->
        <Button
            android:id="@+id/btn_exit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:minHeight="56dp"
            android:text="退出应用"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            app:icon="@drawable/ic_exit"
            app:iconGravity="start" />

    </LinearLayout>

    <!-- 底部提示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Seeq需要网络连接来提供个性化EQ服务"
        android:textSize="14sp"
        android:textColor="@color/text_hint"
        android:gravity="center"
        android:alpha="0.8" />

</LinearLayout>
