[{"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$2.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$2.class", "size": 1568, "crc": -**********}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$5.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$5.class", "size": 1630, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$DataSetChangeObserver.class", "name": "androidx/viewpager2/widget/ViewPager2$DataSetChangeObserver.class", "size": 1579, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$4.class", "name": "androidx/viewpager2/widget/ViewPager2$4.class", "size": 1770, "crc": -881672185}, {"key": "androidx/viewpager2/widget/ViewPager2$LinearLayoutManagerImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$LinearLayoutManagerImpl.class", "size": 3198, "crc": -**********}, {"key": "androidx/viewpager2/widget/ScrollEventAdapter$ScrollEventValues.class", "name": "androidx/viewpager2/widget/ScrollEventAdapter$ScrollEventValues.class", "size": 710, "crc": 824875439}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$DataSetChangeObserver.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$DataSetChangeObserver.class", "size": 1655, "crc": 1219955183}, {"key": "androidx/viewpager2/widget/ViewPager2$1.class", "name": "androidx/viewpager2/widget/ViewPager2$1.class", "size": 969, "crc": 2076484730}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$3.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$3.class", "size": 1790, "crc": -1932450139}, {"key": "androidx/viewpager2/widget/ScrollEventAdapter.class", "name": "androidx/viewpager2/widget/ScrollEventAdapter.class", "size": 8945, "crc": -1149825412}, {"key": "androidx/viewpager2/widget/ViewPager2$Orientation.class", "name": "androidx/viewpager2/widget/ViewPager2$Orientation.class", "size": 652, "crc": -987981294}, {"key": "androidx/viewpager2/widget/ViewPager2$SavedState$1.class", "name": "androidx/viewpager2/widget/ViewPager2$SavedState$1.class", "size": 1899, "crc": 1321318537}, {"key": "androidx/viewpager2/widget/AnimateLayoutChangeDetector$1.class", "name": "androidx/viewpager2/widget/AnimateLayoutChangeDetector$1.class", "size": 1027, "crc": -1522355160}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$1.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$1.class", "size": 1306, "crc": 1870813796}, {"key": "androidx/viewpager2/widget/MarginPageTransformer.class", "name": "androidx/viewpager2/widget/MarginPageTransformer.class", "size": 1969, "crc": 715077114}, {"key": "androidx/viewpager2/widget/ViewPager2$ScrollState.class", "name": "androidx/viewpager2/widget/ViewPager2$ScrollState.class", "size": 652, "crc": 1091491762}, {"key": "androidx/viewpager2/widget/CompositePageTransformer.class", "name": "androidx/viewpager2/widget/CompositePageTransformer.class", "size": 1632, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$3.class", "name": "androidx/viewpager2/widget/ViewPager2$3.class", "size": 1089, "crc": -103595031}, {"key": "androidx/viewpager2/adapter/StatefulAdapter.class", "name": "androidx/viewpager2/adapter/StatefulAdapter.class", "size": 365, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$1.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$1.class", "size": 1568, "crc": 393535664}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$1.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$1.class", "size": 1655, "crc": -663763702}, {"key": "androidx/viewpager2/widget/ViewPager2$BasicAccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$BasicAccessibilityProvider.class", "size": 2418, "crc": **********}, {"key": "androidx/viewpager2/widget/PageTransformerAdapter.class", "name": "androidx/viewpager2/widget/PageTransformerAdapter.class", "size": 2625, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$SavedState.class", "name": "androidx/viewpager2/widget/ViewPager2$SavedState.class", "size": 2032, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$OffscreenPageLimit.class", "name": "androidx/viewpager2/widget/ViewPager2$OffscreenPageLimit.class", "size": 724, "crc": 185485528}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$4.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$4.class", "size": 808, "crc": -321196472}, {"key": "androidx/viewpager2/widget/ViewPager2$AccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$AccessibilityProvider.class", "size": 4138, "crc": -936657506}, {"key": "androidx/viewpager2/widget/ViewPager2$OnPageChangeCallback.class", "name": "androidx/viewpager2/widget/ViewPager2$OnPageChangeCallback.class", "size": 896, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$3.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$3.class", "size": 1209, "crc": 925791801}, {"key": "androidx/viewpager2/widget/FakeDrag.class", "name": "androidx/viewpager2/widget/FakeDrag.class", "size": 3739, "crc": -802264046}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$2.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$2.class", "size": 1248, "crc": -806007038}, {"key": "androidx/viewpager2/adapter/FragmentViewHolder.class", "name": "androidx/viewpager2/adapter/FragmentViewHolder.class", "size": 1573, "crc": 19485826}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$2.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$2.class", "size": 1842, "crc": -**********}, {"key": "androidx/viewpager2/widget/AnimateLayoutChangeDetector.class", "name": "androidx/viewpager2/widget/AnimateLayoutChangeDetector.class", "size": 3672, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$2.class", "name": "androidx/viewpager2/widget/ViewPager2$2.class", "size": 1281, "crc": 999743378}, {"key": "androidx/viewpager2/widget/ViewPager2.class", "name": "androidx/viewpager2/widget/ViewPager2.class", "size": 22122, "crc": -1282376223}, {"key": "META-INF/androidx.viewpager2_viewpager2.version", "name": "META-INF/androidx.viewpager2_viewpager2.version", "size": 6, "crc": -42031000}, {"key": "androidx/viewpager2/widget/ViewPager2$PagerSnapHelperImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$PagerSnapHelperImpl.class", "size": 1156, "crc": -1774004392}, {"key": "androidx/viewpager2/widget/ViewPager2$RecyclerViewImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$RecyclerViewImpl.class", "size": 2254, "crc": -280226171}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3.class", "size": 1464, "crc": 1669676812}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter.class", "size": 16156, "crc": -85209582}, {"key": "androidx/viewpager2/widget/ViewPager2$PageTransformer.class", "name": "androidx/viewpager2/widget/ViewPager2$PageTransformer.class", "size": 372, "crc": -240051477}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer.class", "size": 5498, "crc": -138061911}, {"key": "androidx/viewpager2/widget/CompositeOnPageChangeCallback.class", "name": "androidx/viewpager2/widget/CompositeOnPageChangeCallback.class", "size": 2710, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider.class", "size": 8282, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$SmoothScrollToPosition.class", "name": "androidx/viewpager2/widget/ViewPager2$SmoothScrollToPosition.class", "size": 879, "crc": **********}]